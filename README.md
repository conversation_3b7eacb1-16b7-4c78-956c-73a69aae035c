
# 简单工作流

这是一个简易的多自定义节点的工作流服务, 暂不支持条件节点. 如果需要支持, 可能需要定义多个流程利用 hook 进行串联创建. 
这样条件的创建就需要业务方自己实现, 保证整个工作流的功能简洁高效.

## 功能介绍

- 支持定义多个工作流, 每个工作流也支持不同分类的流程定义 
- 每个流程定义支持多个节点, 每个节点支持不同类型的任务
- 每个节点支持不同类型的选人策略, 支持多人的或签审批
- 节点审批支持同意, 拒绝, 退回, 转办等操作
- 节点类型支持自定义, 需要自己实现选人策略
- 任务节点审批通过后会触发Hook, 会推送到RabbitMQ中, 其他的业务方支持监听已完成自己的自定义功能

## API 介绍
项目支持Feign请求和HTTP请求.

### Feign 请求
项目集成dh-dto之后, 可以直接使用Feign请求, 例如:

```java
import com.dh.dto.bean.dto.workflow.enums.ApproveTypeEnum;

class TestController {
    @Resource
    FlowFeignService flowFeignService;

    public void test() {
        // 创建任务
        FlowTaskDTO flowTaskDTO = new FlowTaskDTO();
        R<CreateRespDTO> task = flowFeignService.createTask(flowTaskDTO);
        int instanceId = task.getData().getProcessInst().getId();   //业务方需要保存, 后续的审批操作需要用到

        //同意
        ApproveReqDTO approveReqDTO = new ApproveReqDTO();
        approveReqDTO.setApproveType(ApproveTypeEnum.AGREE);
        approveReqDTO.setInstanceId(instanceId);
        approveReqDTO.setDeadlineSuggestion("做的好");
        flowFeignService.approvalTask(flowTaskDTO);

        //拒绝
        ApproveReqDTO approveReqDTO = new ApproveReqDTO();
        approveReqDTO.setApproveType(ApproveTypeEnum.REFUSE);
        approveReqDTO.setInstanceId(instanceId);
        approveReqDTO.setDeadlineSuggestion("我不想同意啊.dd");
        flowFeignService.approvalTask(flowTaskDTO);

        //移交
        TransferTaskDTO flowTaskDTO = new TransferTaskDTO();
        flowTaskDTO.setInstanceId(instanceId);
        flowTaskDTO.setToUserId(1234567890L);
        flowTaskDTO.setDeadlineSuggestion("帮我审批下");
        flowFeignService.transferTask(flowTaskDTO);
        
        //获得任务信息
        FlowTaskDTO flowTaskDTO = new FlowTaskDTO();
        flowTaskDTO.setInstanceId(instanceId);
        R<WorkflowInfoDTO> resp = flowFeignService.getTask(flowTaskDTO);
    }
}
```

### HTTP 请求
#### 创建任务

```
URL: /workflow/simpleFlow/createTask
Method: POST
Body:
{"processId":<<processId>>,"businessId": <<businessId>>,"extId":<<extId>>}
```

##### 参数介绍
> - processId: 流程ID
> - businessId: 业务ID, 例如: 订单ID, 船舶ID等等都可以
> - extId: 扩展ID 例如: 部门ID, 业务自定义的ID等等都可以

##### 响应介绍
```json5
{
  "code": "OK",
  "msg": "",
  "data": {
    "processInst": {
      "id": "1925396616524800002",  //业务方需要保存, 后续的审批操作需要用到
      "businessId": "205",
      "processId": "3",             //工作流id
      "extId": 1,                   //工作流扩展id
      "statusCode": 1,
      "statusText": "状态 啊 1",
      "deadlineBy": "",
      "createdBy": "",
      "currentProcedureId": "1925396616910675969",
      "createdAt": "2025-05-22 11:41:40",
      "updatedAt": "2025-05-22 11:41:40"
    },
    "processNode": {
      "id": "7",  //节点ID
      "workInstId": "1925396616524800002",
      "processId": "3", //工作流id
      "nodeName": "1级审批",
      "deadlineBy": "1203962861737881601",
      "deadlinePost": "1390586718503075841",
      "type": 3,
      "userList": [
        {
          "userId": "1207580282931564546",
          "realName": "",
          "deptName": "",
          "postName": ""
        }
      ],
      "statusCode": 1,
      "statusText": "状态 啊 1"
    }
  },
  "ok": true
}
```

> processInst.id 业务方需要保存, 后续的审批操作需要用到

---

#### 审批任务 - 同意

```
URL: /workflow/simpleFlow/approveTask
Method: POST
Body:
{"processId":<<processId>>,"businessId": <<businessId>>,"extId":<<extId>>, "instanceId": <<instanceId>>, "deadlineSuggestion":"同意理由(可为空)"}
```

##### 参数介绍

> 方式1:
> - processId: 流程ID
> - businessId: 业务ID, 例如: 订单ID, 船舶ID等等都可以
> - extId: 扩展ID 例如: 部门ID, 业务自定义的ID等等都可以
> - deadlineSuggestion 审批的描述
>
> 方式2:
> - instanceId: 实例ID
> - deadlineSuggestion 审批的描述

##### 响应介绍
```json5
{
  "code": "OK",
  "msg": "",
  "data": {
    "workflowProcedure": {
      "id": "1925380889579274241",
      "processInstId": "1925380889306644482",
      "processNodeId": "7",
      "deadlineBy": "1335776543334572034",
      "currentProcessUsers": "1207580282931564546,1247759532925874177,1275723978448999737,1446303099034562561,1478294160933912577,1499590900311990273,1592781907022639105,1892744422219603970,1896746691864850433,1914515272643969026",
      "deadlineDate": "2025-05-22 10:39:11",
      "deadlineSuggestion": "asdgasdasgasdg",
      "deadlineResult": "同意",
      "startDate": "2025-05-22 10:39:11",
      "endDate": "2025-05-22 10:39:12"
    },
    "processInstance": {
      "id": "1925380889306644482",
      "businessId": "205",
      "processId": "3",
      "extId": 1,
      "statusCode": 2,
      "statusText": "状态 啊 2",
      "deadlineBy": "1335776543334572034",
      "createdBy": "1335776543334572034",
      "currentProcedureId": "1925380899352002561",
      "createdAt": "2025-05-22 10:39:11",
      "updatedAt": "2025-05-22 10:39:12"
    }
  },
  "ok": true
}
```
异常响应:
```json5
{
  "code": "500",
  "msg": "流程已完成，无法再次审批",
  "data": null,
  "ok": false
}
```

---

#### 审批任务 - 拒绝

```
URL: /workflow/simpleFlow/refuseTask
Method: POST
Body:
{"processId":<<processId>>,"businessId": <<businessId>>,"extId":<<extId>>, "instanceId": <<instanceId>>, "deadlineSuggestion":"拒绝理由"}
```

##### 参数介绍

> 方式1:
> - processId: 流程ID
> - businessId: 业务ID, 例如: 订单ID, 船舶ID等等都可以
> - extId: 扩展ID 例如: 部门ID, 业务自定义的ID等等都可以
> - deadlineSuggestion 审批的描述
>
> 方式2:
> - instanceId: 实例ID
> - deadlineSuggestion 审批的描述

##### 响应介绍
```json5
{
  "code": "OK",
  "msg": "",
  "data": {
    "workflowProcedure": {
      "id": "1925380889579274241",
      "processInstId": "1925380889306644482",
      "processNodeId": "7",
      "deadlineBy": "1335776543334572034",
      "currentProcessUsers": "1207580282931564546,1247759532925874177,1275723978448999737,1446303099034562561,1478294160933912577,1499590900311990273,1592781907022639105,1892744422219603970,1896746691864850433,1914515272643969026",
      "deadlineDate": "2025-05-22 10:39:11",
      "deadlineSuggestion": "asdgasdasgasdg",
      "deadlineResult": "同意",
      "startDate": "2025-05-22 10:39:11",
      "endDate": "2025-05-22 10:39:12"
    },
    "processInstance": {
      "id": "1925380889306644482",
      "businessId": "205",
      "processId": "3",
      "extId": 1,
      "statusCode": 2,
      "statusText": "状态 啊 2",
      "deadlineBy": "1335776543334572034",
      "createdBy": "1335776543334572034",
      "currentProcedureId": "1925380899352002561",
      "createdAt": "2025-05-22 10:39:11",
      "updatedAt": "2025-05-22 10:39:12"
    }
  },
  "ok": true
}
```
异常响应:
```json5
{
  "code": "500",
  "msg": "流程已完成，无法再次审批",
  "data": null,
  "ok": false
}
```

---

#### 审批任务 - 撤回

```
URL: /workflow/simpleFlow/revokeTask
Method: POST
Body:
{"processId":<<processId>>,"businessId": <<businessId>>,"extId":<<extId>>, "instanceId": <<instanceId>>, "deadlineSuggestion":"撤回理由"}
```

##### 参数介绍

> 方式1:
> - processId: 流程ID
> - businessId: 业务ID, 例如: 订单ID, 船舶ID等等都可以
> - extId: 扩展ID 例如: 部门ID, 业务自定义的ID等等都可以
> - deadlineSuggestion 审批的描述
>
> 方式2:
> - instanceId: 实例ID
> - deadlineSuggestion 审批的描述

##### 响应介绍
```json5
{
  "code": "OK",
  "msg": "",
  "data": {
    "workflowProcedure": {
      "id": "1925380889579274241",
      "processInstId": "1925380889306644482",
      "processNodeId": "7",
      "deadlineBy": "1335776543334572034",
      "currentProcessUsers": "1207580282931564546,1247759532925874177,1275723978448999737,1446303099034562561,1478294160933912577,1499590900311990273,1592781907022639105,1892744422219603970,1896746691864850433,1914515272643969026",
      "deadlineDate": "2025-05-22 10:39:11",
      "deadlineSuggestion": "asdgasdasgasdg",
      "deadlineResult": "同意",
      "startDate": "2025-05-22 10:39:11",
      "endDate": "2025-05-22 10:39:12"
    },
    "processInstance": {
      "id": "1925380889306644482",
      "businessId": "205",
      "processId": "3",
      "extId": 1,
      "statusCode": 2,
      "statusText": "状态 啊 2",
      "deadlineBy": "1335776543334572034",
      "createdBy": "1335776543334572034",
      "currentProcedureId": "1925380899352002561",
      "createdAt": "2025-05-22 10:39:11",
      "updatedAt": "2025-05-22 10:39:12"
    }
  },
  "ok": true
}
```
异常响应:
```json5
{
  "code": "500",
  "msg": "流程已完成，无法再次审批",
  "data": null,
  "ok": false
}
```

---

#### 获得工作流数据

```
URL: /workflow/simpleFlow/getTask
Method: GET
Body:
{"processId":<<processId>>,"businessId": <<businessId>>,"extId":<<extId>>, "instanceId": <<instanceId>>}
```

##### 参数介绍

> 方式1:
> - processId: 流程ID
> - businessId: 业务ID, 例如: 订单ID, 船舶ID等等都可以
> - extId: 扩展ID 例如: 部门ID, 业务自定义的ID等等都可以
> - deadlineSuggestion 审批的描述
>
> 方式2:
> - instanceId: 实例ID
> - deadlineSuggestion 审批的描述

##### 响应介绍
```json5
{
  "code": "OK",
  "msg": "",
  "data": {
    "instance": { //流程实例
      "id": "1925355748686241793",
      "businessId": "205",
      "processId": "3",
      "extId": 1,
      "statusCode": 1,
      "statusText": "状态 啊 1",
      "deadlineBy": "1207580282931564546",
      "createdBy": "1335776543334572034",
      "currentProcedureId": "1925355749072117761",
      "createdAt": "2025-05-22 08:59:17",
      "updatedAt": "2025-05-22 08:59:17"
    },
    "nodeList": [ //流程节点
      {
        "id": "7",
        "processId": "3",
        "extId": 1,
        "nodeName": "1级审批",
        "deadlineBy": "1203962861737881601",
        "deadlinePost": "1390586718503075841",
        "type": 3,
        "statusCode": 1,
        "statusText": "状态 啊 1",
        "hookName": null
      }, //...
    ],
    "procedureList": [  //流程流转记录
      {
        "id": "1925355749072117761",
        "processInstId": "1925355748686241793",
        "processNodeId": "7",
        "deadlineBy": null,
        "deadlineDate": "2025-05-22 08:59:17",
        "deadlineSuggestion": null,
        "deadlineResult": null,
        "startDate": "2025-05-22 08:59:17",
        "endDate": null,
        "realName": null,
        "deptName": null,
        "postName": null
      }
    ]
  },
  "ok": true
}
```

## 使用简介

### 引用依赖

### 定义流程

> 在平台的 基础信息/工作流配置/流程定义 中定义业务的流程, 并配置好节点的审批人

### 创建流程
> 在业务系统中调用创建流程的接口, 传入流程ID, 业务ID, 扩展ID等参数, 即可创建流程

### 审批流程
> 在业务系统中调用审批流程的接口, 传入流程ID, 业务ID, 扩展ID等参数, 即可审批流程

### 读取流程数据, 显示在业务的UI界面中

---
### 注意事项 
> - 每一个业务同时只能创建一个流程, 如果审批通过后不得再次创建流程,
> - 如果需要一个工作流程需要按照不同属性创建流程, 可以在业务系统中定义流程分类的列表, 然后在创建流程时, 随机选择一个流程分类ID创建流程
> - 流程节点审批通过会触发Hook, 会推送到RabbitMQ中, 其他的业务方支持监听已完成业务方的自定义功能, 实现和业务系统的解耦
> - 拒绝/撤回的节点审批, 后续可以再次发起新的流程, 完全重新开始(之前的数据有留存, 可以读取出来历史记录)