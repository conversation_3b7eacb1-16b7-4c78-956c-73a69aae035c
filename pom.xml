<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dh-parent</artifactId>
        <groupId>com.dh</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dh-workflow</artifactId>
    <packaging>${project.packaging}</packaging>

    <properties>
        <flowable.version>6.5.0</flowable.version>
        <!-- 打包类型，默认jar -->
        <project.packaging>jar</project.packaging>
        <profile.env/>
        <profile.swagger/>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dh</groupId>
            <artifactId>dh-common</artifactId>
            <version>1.12-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.dh</groupId>
            <artifactId>dh-dto</artifactId>
            <version>8.31-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.dh</groupId>
            <artifactId>dh-framework-security</artifactId>
            <version>2.7-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.jboss.logging</groupId>
            <artifactId>jboss-logging</artifactId>
        </dependency>

        <!-- flowable 6.5-->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter</artifactId>
            <version>${flowable.version}</version>
        </dependency>

        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-form-spring-configurator</artifactId>
            <version>${flowable.version}</version>
        </dependency>

        <!--swagger-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>swagger-bootstrap-ui</artifactId>
            <version>1.9.6</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>

        <!--数据库-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <!-- mybatis-plus-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <!-- mybatis-plus 代码生成，模板引擎 代码生成依赖包-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.28</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.codehaus.groovy/groovy-all -->
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>2.5.12</version>
            <type>pom</type>
        </dependency>

        <!-- 阿里云日志服务 -->
        <dependency>
            <groupId>com.dh</groupId>
            <artifactId>dh-log-starter</artifactId>
            <version>2.6-SNAPSHOT</version>
        </dependency>

        <!-- liquibase -->
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind -->
<!--        <dependency>-->
<!--            <groupId>com.fasterxml.jackson.core</groupId>-->
<!--            <artifactId>jackson-databind</artifactId>-->
<!--            <version>2.9.7</version>-->
<!--        </dependency>-->
        <!-- 数据库加密 -->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>2.1.0</version>
        </dependency>

        <!-- 队列服务 starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 设置maven 打包jdk版本-->
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.3.1</version>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.4.3</version>
                <configuration>
                    <mainClass>com.dh.workflow.WorkFlowApplication</mainClass>
                    <!-- 打包排除依赖 -->
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                        <exclude>
                            <groupId>com.baomidou</groupId>
                            <artifactId>mybatis-plus-generator</artifactId>
                        </exclude>
                        <exclude>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-starter-freemarker</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
                <!--修复没有主程序清单问题：https://www.cnblogs.com/niceboat/p/6230448.html-->
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--配置xls文件不被编码过滤-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
        <!-- 资源文件过滤配置-->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>application.yml</include>
<!--                    <include>application-${env}.yml</include>-->
                    <include>application-${profile.env}.yml</include>
                    <include>**/*.xml</include>
<!--                    <include>**/*.yml</include>-->
                    <include>**/*.xlsx</include>
                    <include>**/*.xls</include>
                    <include>**/*.sql</include>
                    <include>**/*.form</include>
                    <include>**/*.properties</include>
                </includes>
            </resource>
        </resources>
    </build>
    <!-- 预定定义环境配置 -->
    <profiles>
        <profile>
            <id>war</id>
            <properties>
                <project.packaging>war</project.packaging>
            </properties>
            <dependencies>
                <!-- war包排除tomcat相关依赖，放置与tomcat容器冲突 -->
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <scope>provided</scope>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>swagger</id>
            <properties>
                <profile.swagger>,swagger</profile.swagger>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <profile.env>dev</profile.env>
                <spring.profiles.active>${profile.env}${profile.swagger}</spring.profiles.active>
            </properties>
            <!-- 是否默认 true表示默认-->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!-- 预发布环境 -->
        <profile>
            <id>qa</id>
            <properties>
                <profile.env>qa</profile.env>
                <spring.profiles.active>${profile.env}${profile.swagger}</spring.profiles.active>
            </properties>
        </profile>
        <!-- 生产环境 -->
        <profile>
            <id>prod</id>
            <properties>
                <profile.env>prod</profile.env>
                <spring.profiles.active>${profile.env}</spring.profiles.active>
            </properties>
        </profile>
        <!-- 模板环境 -->
        <profile>
            <id>tp</id>
            <properties>
                <profile.env>tp</profile.env>
                <spring.profiles.active>${profile.env}</spring.profiles.active>
            </properties>
        </profile>
    </profiles>
</project>
