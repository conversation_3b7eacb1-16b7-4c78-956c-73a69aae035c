{"key": "visitReportForm", "name": "访船报告", "fields": [{"id": "reportName", "name": "报告名称", "type": "dh_text", "required": true, "readOnly": true}, {"id": "shipName", "name": "船舶", "type": "dh_text", "required": true, "readOnly": true}, {"fieldType": "OptionFormField", "id": "checkType", "name": "检查类型", "type": "dh_dropdown", "required": true, "readOnly": true, "options": [{"id": 0, "name": "访船"}, {"id": 1, "name": "外部检查"}, {"id": 2, "name": "安全专项检查"}]}, {"fieldType": "OptionFormField", "id": "visitType", "name": "跟船方式", "type": "dh_dropdown", "required": false, "readOnly": true, "options": [{"id": 0, "name": "访船"}, {"id": 1, "name": "跟船"}]}, {"id": "boardingTime", "name": "上船日期", "type": "dh_text", "required": true, "readOnly": true}, {"id": "disembarkTime", "name": "下船日期", "type": "dh_text", "required": true, "readOnly": true}, {"id": "checkerName", "name": "访船人", "type": "dh_text", "required": true, "readOnly": true}, {"id": "checkDate", "name": "访船日期", "type": "dh_text", "required": true, "readOnly": true}, {"fieldType": "OptionFormField", "id": "reportState", "name": "状态", "type": "dh_dropdown", "required": true, "readOnly": true, "options": [{"id": 0, "name": "未审核"}, {"id": 1, "name": "审核中"}, {"id": 2, "name": "已审核"}, {"id": 3, "name": "审核不通过"}]}, {"id": "portName", "name": "港口", "type": "dh_text", "required": true, "readOnly": true}, {"id": "visitRoleName", "name": "访船部门", "type": "dh_text", "required": true, "readOnly": true}, {"fieldType": "OptionFormField", "id": "threeFlag", "name": "三无实现情况", "type": "dh_dropdown", "required": true, "readOnly": true, "options": [{"id": 0, "name": "否"}, {"id": 1, "name": "是"}]}, {"id": "visitEntourageStr", "name": "同访人员", "type": "dh_text", "required": true, "readOnly": true}, {"id": "sharing", "name": "亮点及经验分享", "type": "dh_multi-line-text", "required": false, "readOnly": true}, {"id": "shipManageNode", "name": "总结船舶的管理状况和设备的维护保养状况", "type": "dh_multi-line-text", "required": false, "readOnly": true}, {"id": "faultNode", "name": "通报所发现的缺陷和主要问题，分析导致缺陷和问题的根本原因", "type": "dh_multi-line-text", "required": false, "readOnly": true}, {"id": "faultNode", "name": "分享典型的减少和避免船员伤害、机损或海损的事故案例（LFI）", "type": "dh_multi-line-text", "required": false, "readOnly": true}, {"id": "deadlineNode", "name": "向船长下达“限期整改通知书”，布置纠正和预防措施", "type": "dh_multi-line-text", "required": false, "readOnly": true}, {"id": "management", "name": "管理方面的不足及改进措施", "type": "dh_multi-line-text", "required": false, "readOnly": true}, {"id": "incentives", "name": "层次的激励、管理制度等不足和改进措施", "type": "dh_multi-line-text", "required": false, "readOnly": true}, {"id": "integrating_skills", "name": "层次的激励、综合能力、工作态度、培训等不足及改进措施", "type": "dh_multi-line-text", "required": false, "readOnly": true}, {"id": "department", "name": "相关部门对船舶管理不足及改进措施", "type": "dh_multi-line-text", "required": false, "readOnly": true}, {"id": "tableInterview", "name": "船员访谈", "type": "dh_table", "required": true, "readOnly": true}, {"id": "tableSuggest", "name": "访船建议", "type": "dh_table", "required": true, "readOnly": true}, {"id": "tableRecord", "name": "检查记录", "type": "dh_table", "required": true, "readOnly": true}, {"id": "tableConcern", "name": "重点关注", "type": "dh_table", "required": true, "readOnly": true}, {"id": "shipManagementVisitShipList", "name": "船舶管理部访船模板", "type": "dh_table", "required": true, "readOnly": true}, {"id": "materialVisitShipList", "name": "物资管理部访船清单", "type": "dh_table", "required": true, "readOnly": true}]}