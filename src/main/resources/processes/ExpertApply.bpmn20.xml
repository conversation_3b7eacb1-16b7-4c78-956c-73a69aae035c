<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             xmlns:flowabl="http://flowable.org/bpmn" typeLanguage="http://www.w3.org/2001/XMLSchema"
             expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
    <process id="ExpertApply" name="专家入库申请流程" isExecutable="true">
        <startEvent id="startEvent" name="开始" flowable:initiator="initiator" flowable:formKey="expertForm" flowable:formFieldValidation="true"></startEvent>
        <sequenceFlow id="sequenceFlow-aee5ce9f-a5a3-49bd-9358-992e021740f2" sourceRef="startEvent" targetRef="firstAudit"></sequenceFlow>
        <userTask id="firstAudit" name="初审" flowable:assignee="${employee}" >
            <extensionElements>
                <flowable:formProperty id="allowance" variable="allowance" type="string" name="专家津贴"/>
                <flowable:formProperty id="confirmedOpinion" variable="confirmedOpinion" type="string" name="确认意见" required="true" />
            </extensionElements>
        </userTask>
        <userTask id="endAudit" name="终审" flowable:assignee="${employee}" >
            <extensionElements>
                <flowable:formProperty id="allowance" variable="allowance" type="string" name="专家津贴"/>
                <flowable:formProperty id="confirmedOpinion" variable="confirmedOpinion" type="string" name="确认意见" required="true" />
            </extensionElements>
        </userTask>
        <exclusiveGateway id="sid-3080D13A-92F5-445E-B28C-2A117B8C2E12"></exclusiveGateway>
        <intermediateThrowEvent id="endFalse" name="失败结束"></intermediateThrowEvent>
        <exclusiveGateway id="sid-103B1368-2B60-4D56-93F4-F39E7B050FAA"></exclusiveGateway>
        <endEvent id="end" name="结束"></endEvent>
        <sequenceFlow id="sid-4AA9D2EA-2DAC-4344-9715-A9F57AE750F8" sourceRef="firstAudit" targetRef="sid-3080D13A-92F5-445E-B28C-2A117B8C2E12"></sequenceFlow>
        <sequenceFlow id="sid-2B00F7C1-2DEE-4DC5-A4E9-80CC1B7CF9E6" sourceRef="endAudit" targetRef="sid-103B1368-2B60-4D56-93F4-F39E7B050FAA"></sequenceFlow>
        <sequenceFlow id="endPass" name="通过" sourceRef="sid-103B1368-2B60-4D56-93F4-F39E7B050FAA" targetRef="end">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[${outcome=="通过"}]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="firstRefuse" name="拒绝" sourceRef="sid-3080D13A-92F5-445E-B28C-2A117B8C2E12" targetRef="endFalse">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[${outcome=="拒绝"}]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="firstPass" name="通过" sourceRef="sid-3080D13A-92F5-445E-B28C-2A117B8C2E12" targetRef="endAudit">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[${outcome=="通过"}]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="endRefuse" name="拒绝" sourceRef="sid-103B1368-2B60-4D56-93F4-F39E7B050FAA" targetRef="firstAudit">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[${outcome=="拒绝"}]]></conditionExpression>
        </sequenceFlow>
    </process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_ExpertApply">
        <bpmndi:BPMNPlane bpmnElement="ExpertApply" id="BPMNPlane_ExpertApply">
            <bpmndi:BPMNShape bpmnElement="startEvent" id="BPMNShape_startEvent">
                <omgdc:Bounds height="30.0" width="30.0" x="165.0" y="165.0"></omgdc:Bounds>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="firstAudit" id="BPMNShape_firstAudit">
                <omgdc:Bounds height="60.0" width="100.0" x="245.0" y="150.0"></omgdc:Bounds>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="endAudit" id="BPMNShape_endAudit">
                <omgdc:Bounds height="60.0" width="100.0" x="395.0" y="150.0"></omgdc:Bounds>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="sid-3080D13A-92F5-445E-B28C-2A117B8C2E12" id="BPMNShape_sid-3080D13A-92F5-445E-B28C-2A117B8C2E12">
                <omgdc:Bounds height="40.0" width="40.0" x="275.0" y="285.0"></omgdc:Bounds>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="endFalse" id="BPMNShape_endFalse">
                <omgdc:Bounds height="30.0" width="30.0" x="165.0" y="290.0"></omgdc:Bounds>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="sid-103B1368-2B60-4D56-93F4-F39E7B050FAA" id="BPMNShape_sid-103B1368-2B60-4D56-93F4-F39E7B050FAA">
                <omgdc:Bounds height="40.0" width="40.0" x="425.0" y="45.0"></omgdc:Bounds>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="end" id="BPMNShape_end">
                <omgdc:Bounds height="28.0" width="28.0" x="555.0" y="51.0"></omgdc:Bounds>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge bpmnElement="endRefuse" id="BPMNEdge_endRefuse">
                <omgdi:waypoint x="425.02742616033754" y="65.02742616033755"></omgdi:waypoint>
                <omgdi:waypoint x="293.875" y="62.0"></omgdi:waypoint>
                <omgdi:waypoint x="294.71398305084745" y="150.0"></omgdi:waypoint>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="sid-2B00F7C1-2DEE-4DC5-A4E9-80CC1B7CF9E6" id="BPMNEdge_sid-2B00F7C1-2DEE-4DC5-A4E9-80CC1B7CF9E6">
                <omgdi:waypoint x="445.1307860262009" y="150.0"></omgdi:waypoint>
                <omgdi:waypoint x="445.4166666666667" y="84.49102282704126"></omgdi:waypoint>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="sequenceFlow-aee5ce9f-a5a3-49bd-9358-992e021740f2" id="BPMNEdge_sequenceFlow-aee5ce9f-a5a3-49bd-9358-992e021740f2">
                <omgdi:waypoint x="194.94997500444674" y="180.0"></omgdi:waypoint>
                <omgdi:waypoint x="207.0" y="180.0"></omgdi:waypoint>
                <omgdi:waypoint x="207.0" y="180.0"></omgdi:waypoint>
                <omgdi:waypoint x="245.0" y="180.0"></omgdi:waypoint>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="sid-4AA9D2EA-2DAC-4344-9715-A9F57AE750F8" id="BPMNEdge_sid-4AA9D2EA-2DAC-4344-9715-A9F57AE750F8">
                <omgdi:waypoint x="295.1193227091633" y="209.95000000000002"></omgdi:waypoint>
                <omgdi:waypoint x="295.42" y="285.42"></omgdi:waypoint>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="firstRefuse" id="BPMNEdge_firstRefuse">
                <omgdi:waypoint x="275.4130434782609" y="305.4130434782609"></omgdi:waypoint>
                <omgdi:waypoint x="194.94961752190505" y="305.06471805163324"></omgdi:waypoint>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="firstPass" id="BPMNEdge_firstPass">
                <omgdi:waypoint x="314.2541506917771" y="305.6896580483736"></omgdi:waypoint>
                <omgdi:waypoint x="443.875" y="307.0"></omgdi:waypoint>
                <omgdi:waypoint x="444.73425196850394" y="209.95000000000002"></omgdi:waypoint>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="endPass" id="BPMNEdge_endPass">
                <omgdi:waypoint x="464.5192729488201" y="65.42276422764228"></omgdi:waypoint>
                <omgdi:waypoint x="555.0001095809978" y="65.05647747733993"></omgdi:waypoint>
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</definitions>