<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="repairApply" name="抢修申请" isExecutable="true">
    <documentation>抢修申请工作流</documentation>
    <startEvent id="startEvent1" flowable:initiator="initiator" flowable:formKey="repairApplyForm" flowable:formFieldValidation="true"></startEvent>
    <scriptTask id="firstScript" scriptFormat="JavaScript" flowable:autoStoreVariables="true">
      <script><![CDATA[var decision = "[{'deci':'初审通过'},{'deci':'初审驳回'}]";
                    execution.setVariable("decision",decision);]]></script>
    </scriptTask>
    <userTask id="firstAudit" name="初审" flowable:formFieldValidation="true"></userTask>
    <scriptTask id="finalScript" scriptFormat="JavaScript" flowable:autoStoreVariables="true">
      <script><![CDATA[var decision = "[{'deci':'终审通过'},{'deci':'终审驳回'}]";
                    execution.setVariable("decision",decision);]]></script>
    </scriptTask>
    <userTask id="finalAudit" name="终审" flowable:formFieldValidation="true"></userTask>
    <sequenceFlow id="sid-5DD8A813-54E5-4E43-B367-AEC2592225B5" sourceRef="finalScript" targetRef="finalAudit"></sequenceFlow>
    <scriptTask id="updateState" scriptFormat="groovy" flowable:autoStoreVariables="false">
      <script><![CDATA[import com.dh.workflow.Client.BSH;
                    import org.flowable.engine.runtime.ProcessInstance;
                    List<ProcessInstance> pi = runtimeService.createProcessInstanceQuery().processDefinitionKey("repairApply").list();
                    def businessKey = pi[0].getBusinessKey();
                    if("终审通过".equals(deci)){
                        BSH.sendRequest("equipment","updateRepairApply","businessKey="+businessKey+"#state=2");
                    }else{
                        BSH.sendRequest("equipment","updateRepairApply","businessKey="+businessKey+"#state=3");
                    }]]></script>
    </scriptTask>
    <endEvent id="sid-C38F1C2B-C3D2-48E7-B165-2C8C75B14260"></endEvent>
    <sequenceFlow id="sid-F8FA24B1-7142-4C80-A5E9-CA438266515C" sourceRef="updateState" targetRef="sid-C38F1C2B-C3D2-48E7-B165-2C8C75B14260"></sequenceFlow>
    <sequenceFlow id="sid-6DF75F2B-4D67-4077-9C45-7C56E19FAFDB" sourceRef="firstScript" targetRef="firstAudit"></sequenceFlow>
    <exclusiveGateway id="sid-78A502FC-F7A8-495D-BE97-1895DB152323"></exclusiveGateway>
    <sequenceFlow id="sid-5EA81675-F3AC-4337-8F1A-457CB0EC2E14" sourceRef="firstAudit" targetRef="sid-78A502FC-F7A8-495D-BE97-1895DB152323"></sequenceFlow>
    <sequenceFlow id="sid-28F31955-A6FD-45A5-9F29-EF0F24FBFF59" sourceRef="startEvent1" targetRef="firstScript"></sequenceFlow>
    <sequenceFlow id="sid-C6B06F35-D9D0-46CD-88D2-3EF4F5C6AA2A" sourceRef="finalAudit" targetRef="updateState"></sequenceFlow>
    <scriptTask id="secondScript" scriptFormat="JavaScript" flowable:autoStoreVariables="true">
      <script><![CDATA[var decision = "[{'deci':'二审通过'},{'deci':'二审驳回'}]";
                    execution.setVariable("decision",decision);]]></script>
    </scriptTask>
    <userTask id="secondAudit" name="二审" flowable:candidateGroups="1196771445488107574" flowable:formFieldValidation="true"></userTask>
    <sequenceFlow id="sid-62905287-02FE-4EE0-93D9-BDDE1113CD55" sourceRef="secondScript" targetRef="secondAudit"></sequenceFlow>
    <exclusiveGateway id="sid-9C944E85-25E1-480F-BE5D-79299E95AB0F"></exclusiveGateway>
    <sequenceFlow id="sid-A61B99E9-43CB-4897-982D-5F2A387B0465" sourceRef="secondAudit" targetRef="sid-9C944E85-25E1-480F-BE5D-79299E95AB0F"></sequenceFlow>
    <sequenceFlow id="sid-A1526511-7F31-420B-97E8-1EF20E124D3A" name="通过" sourceRef="sid-78A502FC-F7A8-495D-BE97-1895DB152323" targetRef="secondScript">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${deci=='初审通过'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-958BF041-E708-4D08-A352-59EE1D327E93" name="不通过" sourceRef="sid-78A502FC-F7A8-495D-BE97-1895DB152323" targetRef="updateState">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${deci=='初审驳回'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-6FBFE298-5C68-4406-A20E-CD6A94DDD0CC" name="不通过" sourceRef="sid-9C944E85-25E1-480F-BE5D-79299E95AB0F" targetRef="finalScript">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${deci=='二审驳回'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-291671C3-EA5D-4E0F-8D93-B5A38BC54567" name="通过" sourceRef="sid-9C944E85-25E1-480F-BE5D-79299E95AB0F" targetRef="updateState">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${deci=='二审通过'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_repairApply">
    <bpmndi:BPMNPlane bpmnElement="repairApply" id="BPMNPlane_repairApply">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="0.0" y="160.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="firstScript" id="BPMNShape_firstScript">
        <omgdc:Bounds height="80.0" width="100.0" x="120.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="firstAudit" id="BPMNShape_firstAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="255.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="finalScript" id="BPMNShape_finalScript">
        <omgdc:Bounds height="80.0" width="100.0" x="690.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="finalAudit" id="BPMNShape_finalAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="825.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="updateState" id="BPMNShape_updateState">
        <omgdc:Bounds height="80.0" width="100.0" x="990.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-C38F1C2B-C3D2-48E7-B165-2C8C75B14260" id="BPMNShape_sid-C38F1C2B-C3D2-48E7-B165-2C8C75B14260">
        <omgdc:Bounds height="28.0" width="28.0" x="1135.0" y="161.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-78A502FC-F7A8-495D-BE97-1895DB152323" id="BPMNShape_sid-78A502FC-F7A8-495D-BE97-1895DB152323">
        <omgdc:Bounds height="40.0" width="40.0" x="285.0" y="300.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="secondScript" id="BPMNShape_secondScript">
        <omgdc:Bounds height="80.0" width="100.0" x="390.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="secondAudit" id="BPMNShape_secondAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="540.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-9C944E85-25E1-480F-BE5D-79299E95AB0F" id="BPMNShape_sid-9C944E85-25E1-480F-BE5D-79299E95AB0F">
        <omgdc:Bounds height="40.0" width="40.0" x="570.0" y="285.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-28F31955-A6FD-45A5-9F29-EF0F24FBFF59" id="BPMNEdge_sid-28F31955-A6FD-45A5-9F29-EF0F24FBFF59">
        <omgdi:waypoint x="29.94999923927441" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="120.0" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-62905287-02FE-4EE0-93D9-BDDE1113CD55" id="BPMNEdge_sid-62905287-02FE-4EE0-93D9-BDDE1113CD55">
        <omgdi:waypoint x="489.9499999999581" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="540.0" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-5DD8A813-54E5-4E43-B367-AEC2592225B5" id="BPMNEdge_sid-5DD8A813-54E5-4E43-B367-AEC2592225B5">
        <omgdi:waypoint x="789.9499999999999" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="825.0" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-6DF75F2B-4D67-4077-9C45-7C56E19FAFDB" id="BPMNEdge_sid-6DF75F2B-4D67-4077-9C45-7C56E19FAFDB">
        <omgdi:waypoint x="219.94999999995267" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="254.99999999997203" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-6FBFE298-5C68-4406-A20E-CD6A94DDD0CC" id="BPMNEdge_sid-6FBFE298-5C68-4406-A20E-CD6A94DDD0CC">
        <omgdi:waypoint x="609.5071931589537" y="305.43624161073825"></omgdi:waypoint>
        <omgdi:waypoint x="740.0" y="305.0"></omgdi:waypoint>
        <omgdi:waypoint x="740.0" y="214.95000000000002"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-F8FA24B1-7142-4C80-A5E9-CA438266515C" id="BPMNEdge_sid-F8FA24B1-7142-4C80-A5E9-CA438266515C">
        <omgdi:waypoint x="1089.949999999996" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="1135.0" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-A61B99E9-43CB-4897-982D-5F2A387B0465" id="BPMNEdge_sid-A61B99E9-43CB-4897-982D-5F2A387B0465">
        <omgdi:waypoint x="590.1530651340996" y="214.95000000000002"></omgdi:waypoint>
        <omgdi:waypoint x="590.4230769230769" y="285.4230769230769"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-5EA81675-F3AC-4337-8F1A-457CB0EC2E14" id="BPMNEdge_sid-5EA81675-F3AC-4337-8F1A-457CB0EC2E14">
        <omgdi:waypoint x="305.1372852233677" y="214.95000000000002"></omgdi:waypoint>
        <omgdi:waypoint x="305.4310344827586" y="300.4310344827586"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-958BF041-E708-4D08-A352-59EE1D327E93" id="BPMNEdge_sid-958BF041-E708-4D08-A352-59EE1D327E93">
        <omgdi:waypoint x="305.49999999999994" y="339.43618804664726"></omgdi:waypoint>
        <omgdi:waypoint x="305.5" y="389.0"></omgdi:waypoint>
        <omgdi:waypoint x="1040.0" y="389.0"></omgdi:waypoint>
        <omgdi:waypoint x="1040.0" y="214.95000000000002"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-291671C3-EA5D-4E0F-8D93-B5A38BC54567" id="BPMNEdge_sid-291671C3-EA5D-4E0F-8D93-B5A38BC54567">
        <omgdi:waypoint x="590.5" y="324.43127470355734"></omgdi:waypoint>
        <omgdi:waypoint x="590.5" y="356.0"></omgdi:waypoint>
        <omgdi:waypoint x="991.0" y="356.0"></omgdi:waypoint>
        <omgdi:waypoint x="1029.171270718232" y="214.95000000000002"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C6B06F35-D9D0-46CD-88D2-3EF4F5C6AA2A" id="BPMNEdge_sid-C6B06F35-D9D0-46CD-88D2-3EF4F5C6AA2A">
        <omgdi:waypoint x="924.9499999999836" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="989.9999999999847" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-A1526511-7F31-420B-97E8-1EF20E124D3A" id="BPMNEdge_sid-A1526511-7F31-420B-97E8-1EF20E124D3A">
        <omgdi:waypoint x="324.4429606240713" y="320.5"></omgdi:waypoint>
        <omgdi:waypoint x="440.0" y="320.5"></omgdi:waypoint>
        <omgdi:waypoint x="440.0" y="214.95000000000002"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>