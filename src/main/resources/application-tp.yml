spring:
  rabbitmq:
    connection-timeout: 15000
    host: ${DOCKER_RABBITMQ_HOST:}
    port: ${DOCKER_RABBITMQ_PORT:}
    username: ${DOCKER_RABBITMQ_USERNAME:}
    password: ${DOCKER_RABBITMQ_PASSWORD:}
    virtual-host: ${DOCKER_RABBITMQ_VIRTUAL_HOST:}
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${DOCKER_MYSQL_HOST:}/${DOCKER_MYSQL_PLATFORM_NAME:}?useUnicode=true&characterEncoding=UTF8&nullCatalogMeansCurrent=true&useSSL=false&serverTimezone=GMT%2B8
    username: ${DOCKER_MYSQL_PLATFORM_USERNAME:}
    password: ${DOCKER_MYSQL_PLATFORM_PASSWORD:}
    #    password: root
    ##  Hikari 连接池配置 ------ 详细配置请访问：https://github.com/brettwooldridge/HikariCP
    hikari:
      pool-name: ${spring.application.name}
      minimum-idle: 10
      idle-timeout: 600000
      maximum-pool-size: 30
      max-lifetime: 1800000
      auto-commit: true
      connection-timeout: 30000
      validation-timeout: 5000
      connection-test-query: SELECT 1
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 500
        prepStmtCacheSqlLimit: 2048
        useLocalSessionState: true
        rewriteBatchedStatements: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false
  redis:
    port: ${DOCKER_REDIS_PORT:}
    host: ${DOCKER_REDIS_HOST:}
    password: ${DOCKER_REDIS_PASS:}

# 日志配置
logging:
  level:
    root: info
    com.dh.*: debug
  file:
    name: ./log/${spring.application.name}.log
    #单个日志文件大小 MB  GB
    max-size: 20MB
    #日志文件保留天数
    max-history: 7
    #所有文件最多不超过 MB GB
    total-size-cap: 100MB
    clean-history-on-start: true

dh:
  log:
    # 根据不同环境区分
    project: ${DOCKER_LOG_PROJECT:}
    endpoint: ${DOCKER_LOG_ENDPOINT:}   # 内网访问
    accessKeyId: ${DOCKER_ACCESS_KEY_ID:}              #accessKeyId
    accessKeySecret: ${DOCKER_ACCESS_KEY_SECRET:}    #accessKeySecret
    logStore: ${DOCKER_LOG_URL_STORE:}  # 接口日志
    dmlLogStore: ${DOCKER_LOG_DML_STORE:} # dml日志（不填则不记录）
  dto:
    server:
      url: ${DOCKER_INNER_CALL_URL:}
  framework:
    security:
      server:
        white-ips: ${DOCKER_INNER_CALL_IP:}
#file:
#  deleteFile: http://127.0.0.1:8091/file/open/deleteOssFileByFileName
