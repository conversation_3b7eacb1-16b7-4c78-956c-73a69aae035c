--liquibase formatted sql logicalFilePath:liquibase/sql/20250401001_add_workflow.mysql.sql

--changeset zyb:8057-add_workflow-20250401001

ALTER TABLE `workflow_procedure`
    ADD COLUMN `current_process_users` text COMMENT '当前任务处理人列表',
    ADD INDEX (`process_inst_id`);

CREATE TABLE `workflow_extinfo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `process_id` BIGINT DEFAULT NULL COMMENT '工作流ID',
  `ext_id` BIGINT UNSIGNED DEFAULT NULL COMMENT '扩展的ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分类的标题',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;