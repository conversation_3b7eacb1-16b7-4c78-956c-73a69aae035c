spring:
  rabbitmq:
    connection-timeout: 15000
    host: ${RABBITMQ_HOST:*************}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:dh_user}
    password: ${RABBITMQ_PASSWORD:dh2025@dev}
    virtual-host: ${RABBITMQ_VIRTUAL_HOST:/}
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************************
    username: platform_dev
    password: 6OOajQQp
    #    password: root
    ##  Hikari 连接池配置 ------ 详细配置请访问：https://github.com/brettwooldridge/HikariCP
    hikari:
      pool-name: ${spring.application.name}
      minimum-idle: 10
      idle-timeout: 600000
      maximum-pool-size: 30
      max-lifetime: 1800000
      auto-commit: true
      connection-timeout: 30000
      validation-timeout: 5000
      connection-test-query: SELECT 1
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 500
        prepStmtCacheSqlLimit: 2048
        useLocalSessionState: true
        rewriteBatchedStatements: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false
  redis:
    port: 6379
    host: ************
    password: LGD

# 日志配置
logging:
  level:
    root: info
    com.dh.*: debug
  file:
    name: ./log/${spring.application.name}.log
    #单个日志文件大小 MB  GB
    max-size: 20MB
    #日志文件保留天数
    max-history: 7
    #所有文件最多不超过 MB GB
    total-size-cap: 100MB
    clean-history-on-start: true


dh:
  framework:
    security:
      server:
        white-ips:
          - *************
          - ************
          - ***************
  log:
    # 根据不同环境区分
    project: sys-operation-log-dev
  dto:
    server:
      url: ${INNER_CALL_URL:}
#file:
#  deleteFile: http://127.0.0.1:8091/file/open/deleteOssFileByFileName