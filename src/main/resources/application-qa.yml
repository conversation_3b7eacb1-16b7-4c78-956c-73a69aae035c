spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************
    username: test01
    password: 1ybk#T9Y
    #    password: root
    ##  Hikari 连接池配置 ------ 详细配置请访问：https://github.com/brettwooldridge/HikariCP
    hikari:
      pool-name: ${spring.application.name}
      minimum-idle: 5
      idle-timeout: 600000
      maximum-pool-size: 5
      max-lifetime: 1800000
      auto-commit: true
      connection-timeout: 30000
      validation-timeout: 5000
      connection-test-query: SELECT 1
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 500
        prepStmtCacheSqlLimit: 2048
        useLocalSessionState: true
        rewriteBatchedStatements: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false

# 日志配置
logging:
  level:
    root: info
    com.dh.*: debug
  file:
    name: ./log/${spring.application.name}.log
    #单个日志文件大小 MB  GB
    max-size: 20MB
    #日志文件保留天数
    max-history: 7
    #所有文件最多不超过 MB GB
    total-size-cap: 100MB
    clean-history-on-start: true


#登录认证接口
auth:
  login-url: http://127.0.0.1:9090/sys/user/info
  excluded-paths:
    - /actuator/*
    - /webjars/**
    - /v2/**
    - /swagger-resources/**
    - /csrf/**
    - /swagger-ui.html
    - /processInstance/startProcess/**

dh:
  identity-user-url: http://127.0.0.1:9090/sys/flowableIdentity/findByUserId
  identity-group-url: http://127.0.0.1:9090/sys/flowableIdentity/findByGroupId
  identity-user-list-url: http://127.0.0.1:9090/sys/flowableIdentity/findUserByCondition
  identity-group-list-url: hhttp://127.0.0.1:9090/sys/flowableIdentity/findGroupByCondition
  platfrom-url: https://qa-api.dh-platform.com/

file:
  deleteFile: http://127.0.0.1:8091/file/open/deleteOssFileByFileName
