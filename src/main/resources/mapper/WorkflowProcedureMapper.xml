<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dh.workflow.dao.WorkflowProcedureMapper">
    <resultMap id="WorkflowProcedure" type="com.dh.dto.bean.dto.workflow.WorkflowProcedureDTO">
        <result column="id" property="id" />
        <result column="process_inst_id" property="processInstId" />
        <result column="process_node_id" property="processNodeId" />
        <result column="deadline_by" property="deadlineBy" />
        <result column="deadline_date" property="deadlineDate" />
        <result column="deadline_suggestion" property="deadlineSuggestion" />
        <result column="deadline_result" property="deadlineResult" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="real_name" property="realName" />
        <result column="dept_name" property="deptName" />
        <result column="post_name" property="postName" />
        <result column="node_name" property="nodeName" />
    </resultMap>

    <select id="queryProcedureList" resultMap="WorkflowProcedure">
        SELECT
            t1.*,
            su.real_name,
            sd.name as dept_name,
            sp.post_name,
            wpn.node_name
        FROM
            workflow_procedure t1
        LEFT JOIN sys_user su on t1.deadline_by = su.id
        LEFT JOIN sys_dept sd on sd.id=su.dept_id
        LEFT JOIN sys_post sp on sp.sys_post_id=su.post_id
        LEFT JOIN workflow_process_node wpn on wpn.id=t1.process_node_id
        WHERE t1.process_inst_id = #{processInstId}
    </select>

    <select id="queryProcedureHistory" resultMap="WorkflowProcedure">
        SELECT
        t1.*,
        su.real_name,
        sd.name as dept_name,
        sp.post_name,
        wpn.node_name
        FROM
        workflow_procedure t1
        LEFT JOIN sys_user su on t1.deadline_by = su.id
        LEFT JOIN sys_dept sd on sd.id=su.dept_id
        LEFT JOIN sys_post sp on sp.sys_post_id=su.post_id
        LEFT JOIN workflow_process_node wpn on wpn.id=t1.process_node_id
        WHERE 1=1
        <if test="instIds != null and instIds.size() > 0">
            AND t1.process_inst_id IN
            <foreach collection="instIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>