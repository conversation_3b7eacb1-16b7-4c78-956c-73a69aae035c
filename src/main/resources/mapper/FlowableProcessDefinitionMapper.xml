<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dh.workflow.dao.FlowableProcessDefinitionMapper">

    <select id="getProcessDefinitionPage" resultType="com.dh.workflow.dto.FlowableProcessDefinitionDTO">
        SELECT
        t.CATEGORY_ AS category,
        t.ID_ AS id,
        t.NAME_ AS NAME,
        t.KEY_ AS `key`,
        t.RESOURCE_NAME_ AS resourceName,
        t.DGRM_RESOURCE_NAME_ as dgrmResourceName,
        t.SUSPENSION_STATE_ as suspensionState,
        t.VERSION_ AS version,
        t.DEPLOYMENT_ID_ AS deploymentId,
        t.TENANT_ID_ AS tenantId,
        fa.file_annex_id as file_annex_id
        FROM
        act_re_procdef t
        left join file_annex fa on t.DEPLOYMENT_ID_ = fa.flowable_table_id
        <where>
            <if test="name != null and name !=''">
                and t.NAME_ like concat('%',#{name},'%')
            </if>
            <if test="categoryId != null and categoryId !=''">
                and t.CATEGORY_ = #{categoryId}
            </if>
        </where>
        order by t.NAME_ ,t.VERSION_ desc
    </select>

    <select id="getById" resultType="com.dh.workflow.dto.FlowableProcessDefinitionDTO">
        SELECT
        t.CATEGORY_ AS category,
        t.ID_ AS id,
        t.NAME_ AS `name`,
        t.KEY_ AS `key`,
        t.RESOURCE_NAME_ AS resourceName,
        t.DGRM_RESOURCE_NAME_ as dgrmResourceName,
        t.SUSPENSION_STATE_ as suspensionState,
        t.VERSION_ AS version,
        t.DEPLOYMENT_ID_ AS deploymentId,
        t.TENANT_ID_ AS tenantId
        FROM
        act_re_procdef t
        where t.ID_ = #{processDefinitionId}
    </select>

    <select id ="findProcessDefByCategoryId" resultType="com.dh.workflow.dto.FlowableProcessDefinitionDTO">
         SELECT
        t.CATEGORY_ AS category,
        t.ID_ AS id,
        t.NAME_ AS `name`,
        t.KEY_ AS `key`,
        t.RESOURCE_NAME_ AS resourceName,
        t.DGRM_RESOURCE_NAME_ as dgrmResourceName,
        t.SUSPENSION_STATE_ as suspensionState,
        t.VERSION_ AS version,
        t.DEPLOYMENT_ID_ AS deploymentId,
        t.TENANT_ID_ AS tenantId
        FROM
        act_re_procdef t
        where t.CATEGORY_ = #{categoryId}
    </select>
</mapper>