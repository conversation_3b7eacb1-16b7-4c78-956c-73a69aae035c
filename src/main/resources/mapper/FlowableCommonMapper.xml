<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dh.workflow.dao.FlowableCommentMapper">

    <select id="getFlowCommentVosByProcessInstanceId" resultType="com.dh.workflow.dto.CommentDTO">
         SELECT
                t1.ID_,
                t1.TYPE_ AS type,
                t1.TIME_ AS time,
                t1.USER_ID_ AS userId,
                t2.FIRST_ AS userName,
                t1.TASK_ID_ AS taskId,
                t1.PROC_INST_ID_ AS processInstanceId,
                t1.MESSAGE_ AS message,
                CONVERT (FULL_MSG_ USING utf8) AS fullMsg
            FROM
                act_hi_comment t1
            INNER JOIN act_id_user t2 ON t1.USER_ID_ = t2.ID_
            WHERE
                t1.PROC_INST_ID_ = #{processInstanceId} and ACTION_='AddComment'
            ORDER BY
            t1.TIME_ ASC
    </select>
</mapper>
