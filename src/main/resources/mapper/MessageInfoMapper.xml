<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dh.workflow.dao.MessageInfoMapper">

    <delete id="deleteByTime">
        DELETE FROM message_info WHERE create_by=#{createBy} AND create_date=#{createDate}
    </delete>
    <select id="findByIndexes" resultType="com.dh.workflow.entity.MessageInfo">
        SELECT * FROM message_info WHERE params_value=#{value} AND create_by_name=#{userName} AND receive_to=#{id}
    </select>
    <select id="findById" resultType="com.dh.workflow.entity.MessageInfo">
        SELECT * FROM message_info WHERE id=#{id}
    </select>

    <update id="upMessageType">
        UPDATE message_info SET handle_flag=1,read_flag=1,handle_date=NOW()  WHERE id=#{id}
    </update>

    <select id="SelMessageId" resultType="int">
        SELECT count(*) FROM message_info where handle_flag=1  and read_flag=1 and id=#{id}
    </select>
</mapper>
