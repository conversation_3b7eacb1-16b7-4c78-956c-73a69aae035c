<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dh.workflow.dao.FlowableProcessInstanceMapper">

    <select id="findProcessInstance" resultType="com.dh.workflow.dto.FlowableProcessInstanceDTO">
        SELECT DISTINCT
        t1.ID_ AS processInstanceId,
        t1.PROC_DEF_ID_ as processDefinitionId,
        t1.NAME_ AS formName,
        t1.TENANT_ID_ AS systemSn,
        t1.BUSINESS_KEY_ AS businessKey,
        t1.START_TIME_ AS startTime,
        t1.END_TIME_ AS endTime,
        t2.ID_ AS starterId,
        t2.FIRST_ as starter
        FROM
        act_hi_procinst t1
        LEFT JOIN sys_user t2 ON t1.START_USER_ID_ = t2.id
        <where>
            <if test="queryDto.userCode != null and queryDto.userCode != '' ">and t1.START_USER_ID_ = #{queryDto.userCode}</if>
            <if test="queryDto.formName != null and queryDto.formName != '' ">and t1.NAME_ like CONCAT('%',#{queryDto.formName},'%')</if>
        </where>
    </select>

    <select id="findHisVariable" resultType="java.util.Map">
        select t1.name_ name,t1.TEXT_ text,t2.BYTES_ bytes ,t1.VAR_TYPE_ type
        from act_hi_varinst t1 left join ACT_GE_BYTEARRAY t2 on t1.BYTEARRAY_ID_ = t2. ID_
        where t1.PROC_INST_ID_= #{processInstanceId}
    </select>
</mapper>