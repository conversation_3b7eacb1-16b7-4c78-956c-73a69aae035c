<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dh.workflow.dao.WorkflowProcessInstMapper">
    <select id="getProcessInstances">
        SELECT WPI.id,
               WP.`name` as title,
               WE.title  as ext_title,
               WPI.status_code,
               WPI.status_text,
               WPI.deadline_by,
               WPI.created_at,
               WPI.updated_at,
               WPI.params
        FROM workflow_process_inst WPI
                 LEFT JOIN workflow_process WP ON WP.id = WPI.process_id
                 LEFT JOIN workflow_extinfo WE ON WE.ext_id = WPI.ext_id
                 LEFT JOIN workflow_process WP ON WP.id = WPI.process_id
                 LEFT JOIN workflow_extinfo WE ON WE.ext_id = WPI.ext_id
        WHERE WP.is_inlist = 1 and MATCH (WPI.deadline_by) AGAINST(#{uid} IN BOOLEAN MODE);

    </select>
</mapper>