<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dh.workflow.dao.WorkflowProcessNodeMapper">
    <resultMap id="user" type="com.dh.dto.bean.dto.workflow.UserDTO">
        <result column="user_id" property="userId" />
        <result column="real_name" property="realName" />
        <result column="dept_name" property="deptName" />
        <result column="post_name" property="postName" />
    </resultMap>

    <select id="queryPostUsers" resultMap="user">
        SELECT sudp.user_id, su.real_name, sd.name as dept_name
        FROM sys_user_dept_post sudp
        LEFT JOIN sys_user su ON su.id = sudp.user_id
        LEFT JOIN sys_dept sd ON sd.id = su.dept_id
        LEFT JOIN sys_post sp ON sp.sys_post_id = su.post_id
        WHERE sudp.sys_post_id = #{postId} and su.del_flag=0
    </select>

    <select id="queryUsers" resultMap="user">
        SELECT su.id as user_id, su.real_name, sd.name as dept_name, sp.post_name as post_name
        FROM sys_user su
        LEFT JOIN sys_dept sd ON sd.id = su.dept_id
        LEFT JOIN sys_post sp ON sp.sys_post_id = su.post_id
        WHERE su.del_flag=0 and FIND_IN_SET(su.id, #{deadlineByUserIds})
    </select>

    <select id="queryDeptPostUsers" resultMap="user">
        SELECT udp.user_id as user_id, su.real_name, sd.name as dept_name, sp.post_name as post_name
        FROM sys_user_dept_post udp
        LEFT JOIN sys_user su ON su.id = udp.user_id
        LEFT JOIN sys_dept sd ON sd.id = udp.sys_dept_id
        LEFT JOIN sys_post sp ON sp.sys_post_id = udp.sys_post_id
        WHERE udp.sys_dept_id=#{param1} and udp.sys_post_id=#{param2} and su.status=1 and su.del_flag=0
    </select>

    <select id="queryDeptManagerUsers" resultMap="user">
        SELECT
            IF(ISNULL(sd.vice_president),su.id,sd.vice_president) AS user_id,
            IF(ISNULL(sd.vice_president),su.real_name,sdu.real_name) AS real_name,
            IF(ISNULL(sd.vice_president),sd.name,sd2.name) AS dept_name,
            IF(ISNULL(sd.vice_president),sp.post_name,sp2.post_name) AS post_name
        FROM sys_user su
                 LEFT JOIN sys_dept sd ON sd.id = su.dept_id
                 LEFT JOIN sys_post sp ON sp.sys_post_id = su.post_id
                 LEFT JOIN sys_user sdu ON sdu.id = sd.vice_president
                 LEFT JOIN sys_dept sd2 ON sd2.id = sdu.dept_id
                 LEFT JOIN sys_post sp2 ON sdu.post_id = sp2.sys_post_id
        WHERE su.id = #{userId}
    </select>

    <select id="getNodeListById" resultType="com.dh.workflow.bean.vo.WorkflowProcessNodeVO">
        SELECT spn.*,sp.post_name AS deadlinePostName,
               IF(spn.type = 3,CONCAT(sd.`name`,'(部门)'),
                  IF(spn.type = 1,(SELECT GROUP_CONCAT( DISTINCT username SEPARATOR ',' ) AS trackPerson FROM sys_user WHERE FIND_IN_SET(id, spn.deadline_by_user_ids)),null)
                   ) AS deadlineName
        FROM workflow_process_node spn
                 LEFT JOIN sys_post sp ON sp.sys_post_id = spn.deadline_post
                 LEFT JOIN sys_dept sd ON sd.id = spn.deadline_by
                 LEFT JOIN sys_user su ON su.id = spn.deadline_by
        WHERE process_id = #{workflowExtinfoFM.processId} and ext_id = #{workflowExtinfoFM.extId} and spn.is_approver_config = 0
    </select>
    <select id="querySuperiorUsers" resultType="com.dh.dto.bean.dto.workflow.UserDTO">
        WITH RECURSIVE user_hierarchy AS (
            -- 基础查询：获取初始用户(level=1)
            SELECT
                su.id,
                su.real_name,
                su.leader_id,
                sd.name AS dept_name,
                sp.post_name,
                0 AS level,
                FALSE AS is_max_level  -- 标记是否为最大层级
            FROM sys_user su
                     LEFT JOIN sys_dept sd ON su.dept_id = sd.id AND sd.del_flag = 0
                     LEFT JOIN sys_post sp ON su.post_id = sp.sys_post_id AND sp.del_flag = 0
            WHERE su.id = #{createBy}

            UNION ALL

            -- 递归查询：获取上级
            SELECT
                u.id,
                u.real_name,
                u.leader_id,
                d.name AS dept_name,
                p.post_name,
                uh.level + 1,
                CASE WHEN u.leader_id IS NULL THEN TRUE ELSE uh.is_max_level END AS is_max_level
            FROM sys_user u
                     JOIN user_hierarchy uh ON u.id = uh.leader_id
                     LEFT JOIN sys_dept d ON u.dept_id = d.id AND d.del_flag = 0
                     LEFT JOIN sys_post p ON u.post_id = p.sys_post_id AND p.del_flag = 0
            WHERE uh.level &lt; #{level}  -- 按查询的最大可能层级限制(此处为4)
        )
        -- 主查询：优先返回请求层级，若无则返回最大层级
        SELECT t.id AS userId,t.real_name,t.dept_name,t.post_name FROM (
           SELECT *,
                  ROW_NUMBER() OVER (ORDER BY CASE WHEN level = #{level} THEN 0 ELSE 1 END, level DESC) AS rn
           FROM user_hierarchy
           WHERE level = #{level} OR is_max_level = TRUE
        ) t
        WHERE rn = 1;
    </select>
    <resultMap id="NodeMap" type="com.dh.workflow.bean.vo.WorkflowProcessCCNodeVO">
        <result column="process_id" property="processId" />
        <result column="ext_id" property="extId" />
        <result column="node_name" property="nodeName" />
        <result column="is_approver_config" property="isApproverConfig" />
        <collection property="nodeList" ofType="com.dh.workflow.bean.vo.CCConfigVO">
            <result column="type" property="type" />
            <result column="deadline_by" property="deadlineBy" />
            <result column="deadline_post" property="deadlinePost" />
            <result column="deadline_by_user_ids" property="deadlineByUserIds" />
        </collection>
    </resultMap>
    <select id="getCCInfoById" resultMap="NodeMap">
        SELECT spn.*
        FROM workflow_process_node spn
        WHERE process_id = #{processId} and ext_id = #{extId} and spn.is_approver_config = 1
    </select>
    <select id="queryApproveUserId" resultType="com.dh.dto.bean.dto.workflow.UserDTO">
        SELECT su.id as user_id, su.real_name, sd.name as dept_name, sp.post_name as post_name
        FROM sys_user su
                 LEFT JOIN sys_dept sd ON sd.id = su.dept_id
                 LEFT JOIN sys_post sp ON sp.sys_post_id = su.post_id
        WHERE su.del_flag=0 and su.id = #{createdBy}
    </select>
</mapper>