<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dh.workflow.dao.WorkflowProcessMapper">

    <select id="getWorkFlowProcessList" resultType="com.dh.workflow.bean.vo.WorkflowProcessVO">
        select id,`version`,`name`,`key`,is_mulit as isMulit,hook_name,is_todo,todo_route from workflow_process
        <where>
            <if test="workflowProcessFM.name != null and workflowProcessFM.name != ''">
                and `name` like concat('%',#{workflowProcessFM.name},'%')
            </if>
            <if test="workflowProcessFM.key != null and workflowProcessFM.key != ''">
                and `key` like concat('%',#{workflowProcessFM.key},'%')
            </if>
        </where>
        order by id desc
    </select>

    <resultMap id="treeList" type="com.dh.workflow.bean.vo.WorkflowTreeListVO">
        <id property="id" column="wpId"/>
        <result property="name" column="name"/>
        <result property="version" column="version"/>
        <result property="key" column="key"/>
        <result property="isMulit" column="is_mulit"/>
        <result property="title" column="nameToTitle"/>
        <result property="expand" column="expand"/>
        <collection  property="children" ofType="com.dh.workflow.entity.WorkflowExtinfo">
            <id property="id" column="weId"/>
            <result property="processId" column="process_id"/>
            <result property="extId" column="ext_id"/>
            <result property="title" column="title"/>
        </collection>
    </resultMap>

    <select id="getTreeList" resultMap="treeList">
        SELECT wp.id AS wpId,wp.version,wp.`name`,wp.`name` AS nameToTitle,'false' AS expand,wp.`key`,wp.is_mulit,we.id AS weId,we.process_id,we.ext_id,we.title
        FROM workflow_process wp
                 LEFT JOIN workflow_extinfo we ON we.process_id = wp.id
    </select>

    <select id="getFormItemFiles" resultType="com.dh.dto.bean.dto.file.InfraFileDTO">
        SELECT
        id,file_name,old_file_name,path,url_file,type,size,table_type,domain,table_id,upload_time,update_by,update_date,create_by,create_date
        FROM infra_file
        WHERE id IN<foreach collection="fileIds" item="fid" open=" (" separator="," close=")">#{fid}</foreach>
    </select>
</mapper>