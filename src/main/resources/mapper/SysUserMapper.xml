<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dh.workflow.dao.SysUserMapper">

    <select id="findDeptLeader" resultType="com.dh.workflow.entity.SysUser">
        select u2.*
        from sys_user u left join sys_dept d on u.dept_id = d.id
        left join sys_user u2 on d.head = u2.id
        where u.id = #{userId}
    </select>

</mapper>