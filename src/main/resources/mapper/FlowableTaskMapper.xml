<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dh.workflow.dao.FlowableTaskMapper">

    <select id="getToDoTask" resultType="com.dh.workflow.dto.TaskDTO">
        SELECT DISTINCT
            t1.ID_ AS taskId,
            t1.NAME_ AS taskName,
            t2.BUSINESS_KEY_ AS businessKey,
            t2.PROC_INST_ID_ AS processInstanceId,
            t1.CREATE_TIME_ AS startTime,
            t5.KEY_ AS processDefinitionKey,
            t5.NAME_ AS processDefinitionName,
            t6.START_TIME_ AS originatorDate,
            u.username AS originator
        FROM
            act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_re_procdef t5 on t1.PROC_DEF_ID_ = t5.ID_
        LEFT JOIN act_hi_procinst t6 on t1.PROC_INST_ID_ = t6.PROC_INST_ID_  and t1.PROC_DEF_ID_ = t6.PROC_DEF_ID_
        LEFT JOIN sys_user u on t6.START_USER_ID_ = u.id
        WHERE
            t2.BUSINESS_KEY_ IS NOT NULL
        AND (
            t1.ASSIGNEE_ = #{userId}
            OR (
                t1.ASSIGNEE_ IN ( SELECT CONCAT(u.post_id,'') FROM sys_user u where u.id = #{userId})
                OR t1.ASSIGNEE_ IN ( SELECT CONCAT(u.dept_id,'') FROM sys_user u where u.id = #{userId})
            )
            OR (
                (
                    t1.ASSIGNEE_ IS NULL
                    OR t1.ASSIGNEE_ = ''
                )
                AND (
                    t3.USER_ID_ = #{userId}
                    OR t3.GROUP_ID_ IN (SELECT CONCAT(u.dept_id,'') FROM sys_user u WHERE u.id = #{userId} )
                    OR t3.GROUP_ID_ IN (SELECT CONCAT(u.post_id,'') FROM sys_user u WHERE u.id = #{userId} )
                )
            )
        )
        <if test="categoryId != null and categoryId !=''">
            and t5.CATEGORY_ = #{categoryId}
        </if>
        <if test="processDefinitionName != null and processDefinitionName !=''">
            and t5.NAME_ like  concat('%',#{processDefinitionName},'%')
        </if>
    </select>

    <select id="getToDealTask" resultType="com.dh.workflow.dto.TaskDTO">
        SELECT DISTINCT
            t1.ID_ AS taskId,
            t1.NAME_ AS taskName,
            t2.username AS approver,
            t2.id AS approverId,
            t3.BUSINESS_KEY_ AS businessKey,
            t3.PROC_INST_ID_ AS processInstanceId,
            t3.START_TIME_ AS originatorDate,
            u.username AS originator,
            t1.START_TIME_ AS startTime,
            t1.END_TIME_ as endTime,
            t5.KEY_ AS processDefinitionKey,
            t5.NAME_ AS processDefinitionName
        FROM
            act_hi_taskinst t1
        LEFT JOIN sys_user t2 ON t1.ASSIGNEE_ = t2.id
        LEFT JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        LEFT JOIN sys_user u on t3.START_USER_ID_ = u.id
        LEFT JOIN act_re_procdef t5 ON t1.PROC_DEF_ID_ = t5.ID_
        WHERE
            t1.END_TIME_ is not null
        AND t1.ASSIGNEE_ = #{userId}
        <if test="categoryId != null and categoryId !=''">
            and t5.CATEGORY_ = #{categoryId}
        </if>
        <if test="processDefinitionName != null and processDefinitionName !=''">
            and t5.NAME_ like  concat('%',#{processDefinitionName},'%')
        </if>
    </select>

    <select id="getMyInitProcess" resultType="com.dh.workflow.dto.TaskDTO">
        SELECT DISTINCT
            t1.ID_ AS taskId,
            t1.NAME_ AS taskName,
            t2.BUSINESS_KEY_ AS businessKey,
            t2.PROC_INST_ID_ AS processInstanceId,
            t1.CREATE_TIME_ AS startTime,
            t5.KEY_ AS processDefinitionKey,
            t5.NAME_ AS processDefinitionName,
            t6.START_TIME_ AS originatorDate,
            u.username AS originator
        FROM
            act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_re_procdef t5 on t1.PROC_DEF_ID_ = t5.ID_
        LEFT JOIN act_hi_procinst t6 on t1.PROC_INST_ID_ = t6.PROC_INST_ID_  and t1.PROC_DEF_ID_ = t6.PROC_DEF_ID_
        LEFT JOIN sys_user u on t6.START_USER_ID_ = u.id
        WHERE
            t2.BUSINESS_KEY_ IS NOT NULL
        <if test="userId != null and userId !=''">
            and t6.START_USER_ID_ = #{userId}
        </if>
        <if test="categoryId != null and categoryId !=''">
            and t5.CATEGORY_ = #{categoryId}
        </if>
        <if test="processDefinitionName != null and processDefinitionName !=''">
            and t5.NAME_ like  concat('%',#{processDefinitionName},'%')
        </if>
    </select>

    <select id="getMyInitProcessDeal" resultType="com.dh.workflow.dto.TaskDTO">
        select t1.ID_ AS taskId,
            t1.NAME_ AS taskName,
            t2.username AS approver,
            t2.id AS approverId,
            t3.BUSINESS_KEY_ AS businessKey,
            t3.PROC_INST_ID_ AS processInstanceId,
            t3.START_TIME_ AS originatorDate,
            u.username AS originator,
            t1.START_TIME_ AS startTime,
            t1.END_TIME_ as endTime,
            t5.KEY_ AS processDefinitionKey,
            t5.NAME_ AS processDefinitionName
        from act_hi_taskinst t1
        LEFT JOIN sys_user t2 ON t1.ASSIGNEE_ = t2.id
        LEFT JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        LEFT JOIN sys_user u on t3.START_USER_ID_ = u.id
        LEFT JOIN act_re_procdef t5 ON t1.PROC_DEF_ID_ = t5.ID_
        where t1.END_TIME_ is not null
        <if test="userId != null and userId !=''">
            and t3.START_USER_ID_ = #{userId}
        </if>
        <if test="categoryId != null and categoryId !=''">
            and t5.CATEGORY_ = #{categoryId}
        </if>
        <if test="processDefinitionName != null and processDefinitionName !=''">
            and t5.NAME_ like  concat('%',#{processDefinitionName},'%')
        </if>
    </select>

    <select id="getTaskByProcessInstanceId" resultType="com.dh.workflow.dto.TaskDTO">
         SELECT DISTINCT
            t1.ID_ AS taskId,
            t1.NAME_ AS taskName,
            ifnull(t2.username,t6.username) AS approver,
            ifnull(t2.id,t6.id) AS approverId,
            t3.NAME_ AS formName,
            t3.BUSINESS_KEY_ AS businessKey,
            t3.PROC_INST_ID_ AS processInstanceId,
            t3.TENANT_ID_ as systemSn,
            t1.START_TIME_ AS startTime,
            t1.END_TIME_ as endTime,
            t4.MESSAGE_ as message,
            t5.key_ as processDefinitionKey,
            case t4.TYPE_
                when 'ZB' then '转办'
                when 'ZF' then '作废'
                when 'SP'	then '审批'
                when 'BH' then '驳回'
                when 'LCZZ' then '终止流程'
                else '审批'
            end approve_type
        FROM
            act_hi_taskinst t1
        LEFT JOIN sys_user t2 ON t1.ASSIGNEE_ = t2.id
        LEFT JOIN act_hi_procinst t3 ON t1.PROC_INST_ID_ = t3.PROC_INST_ID_
        LEFT JOIN act_hi_comment t4 ON t1.id_ = t4.TASK_ID_ and t1.PROC_INST_ID_ = t4.PROC_INST_ID_
        LEFT JOIN act_re_procdef t5 on t1.PROC_DEF_ID_ = t5.ID_
        LEFT JOIN sys_user t6 on  t4.USER_ID_ = t6.id
        WHERE
            t1.END_TIME_ is not null
        AND t1.PROC_INST_ID_ = #{processInstanceId}
        order by t1.END_TIME_ asc
    </select>

    <select id="findTaskByProcessInstanceId" resultType="com.dh.workflow.dto.TaskDTO">
        SELECT DISTINCT
            t1.ID_ AS taskId,
            t1.NAME_ AS taskName,
            t2.NAME_ AS formName,
            t2.TENANT_ID_ AS systemSn,
            t2.BUSINESS_KEY_ AS businessKey,
            t2.PROC_INST_ID_ AS processInstanceId,
            t1.CREATE_TIME_ AS startTime,
            t5.KEY_ AS processDefinitionKey
        FROM
            act_ru_task t1
        INNER JOIN act_ru_execution t2 ON t1.PROC_INST_ID_ = t2.PROC_INST_ID_
        LEFT JOIN act_ru_identitylink t3 ON t3.TASK_ID_ = t1.ID_
        LEFT JOIN act_re_procdef t5 on t1.PROC_DEF_ID_ = t5.ID_
        WHERE
            t2.BUSINESS_KEY_ IS NOT NULL and t1.PROC_INST_ID_ = #{processInstanceId}
            AND (
                t1.ASSIGNEE_ = #{userId}
                OR (
                    t1.ASSIGNEE_ IN ( SELECT CONCAT(u.post_id,'') FROM sys_user u where u.id = #{userId})
                    OR t1.ASSIGNEE_ IN ( SELECT CONCAT(u.dept_id,'') FROM sys_user u where u.id = #{userId})
                )
                OR (
                    (
                        t1.ASSIGNEE_ IS NULL
                        OR t1.ASSIGNEE_ = ''
                    )
                    AND (
                        t3.USER_ID_ = #{userId}
                        OR t3.GROUP_ID_ IN (SELECT CONCAT(u.dept_id,'') FROM sys_user u WHERE u.id = #{userId} )
                        OR t3.GROUP_ID_ IN (SELECT CONCAT(u.post_id,'') FROM sys_user u WHERE u.id = #{userId} )
                    )
                )
            )
    </select>

    <select id="findAssigneeByTaskId" resultType="com.dh.common.dto.SysUserDTO">
        select u.* from act_ru_task t1
        left join act_ru_identitylink t3 on t1.ID_ = T3.TASK_ID_
        left join sys_user u on t3.USER_ID_ = CONCAT( u.id,'')
        where t1.ID_ = #{taskId} and t3.USER_ID_ IS NOT NULL
        union
        select u.* from act_ru_task t1
        left join act_ru_identitylink t3 on t1.ID_ = T3.TASK_ID_
        left join sys_user u on t3.GROUP_ID_ = CONCAT(u.dept_id,'') or t3.GROUP_ID_ = CONCAT(u.post_id,'')
        where t1.ID_ = #{taskId} and t3.GROUP_ID_ IS NOT NULL
    </select>
</mapper>