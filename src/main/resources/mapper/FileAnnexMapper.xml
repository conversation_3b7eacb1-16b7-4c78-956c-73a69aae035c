<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dh.hr.dao.FileAnnexMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dh.workflow.entity.FileAnnex">
        <id column="file_annex_id" property="fileAnnexId"/>
        <result column="file_Name" property="fileName"/>
        <result column="old_file_Name" property="oldFileName"/>
        <result column="file_type" property="fileType"/>
        <result column="upload_time" property="uploadTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="table_id" property="tableId"/>
        <result column="table_type" property="tableType"/>
        <result column="del_flag" property="delFlag"/>
        <result column="paper_table_id" property="paperTableId"/>
        <result column="oss_folder" property="ossFolder"/>
        <result column="flowable_table_id" property="flowableTableId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        file_annex_id, file_Name, old_file_Name, file_type, upload_time, update_by, update_date, create_by, create_date, table_id, table_type, del_flag, paper_table_id, oss_folder, flowable_table_id
    </sql>

</mapper>
