spring:
  application:
    name: workflow
  profiles:
    active: @spring.profiles.active@
  liquibase:
    enabled: true
    change-log: classpath:/liquibase/db.changelog-master.xml

  #国际化配置
  messages:
    basename: i18n.message
    encoding: utf-8
    cache-duration: PT30M
    use-code-as-default-message: true

#server
server:
  port: ${E_WORKFLOW_PORT:8057}
  servlet:
    context-path: /workflow
    session:
      cookie:
        http-only: true

flowable:
  async-executor-activate: false
  activity-font-name: \u5B8B\u4F53
  label-font-name: \u5B8B\u4F53
  annotation-font-name: \u5B8B\u4F53


# mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath:/mapper/*Mapper.xml
  global-config:
    banner: false
    db-config:  # 伪删除，当在数据库中删除时，将这里的字段改为1
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true

dh:
  framework:
    security:
      auth:
        excluded-paths:
          - /actuator/*
          - /webjars/**
          - /v2/**
          - /swagger-resources/**
          - /csrf/**
          - /swagger-ui.html
          - /processInstance/startProcess/**
  log:
    endpoint: cn-shanghai-intranet.log.aliyuncs.com   # 内网访问
    accessKeyId: ${ACCESS_KEY_ID:}              #accessKeyId
    accessKeySecret: ${ACCESS_KEY_SECRET:}    #accessKeySecret
    logStore: uri-log  # 接口日志
    dmlLogStore: dml-log # dml日志（不填则不记录）
#Spring boot 健康检查
management:
  endpoint:
    health:
      show-details: always

