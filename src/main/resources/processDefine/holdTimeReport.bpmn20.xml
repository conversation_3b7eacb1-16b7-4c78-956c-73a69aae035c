<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="holdTimeReport" name="备舱时间填报" isExecutable="true">
    <documentation>备舱时间填报</documentation>
    <startEvent id="sid-C59BF991-D3C1-4F5C-8DED-CE6453589226" flowable:formKey="holdTimeReportForm" flowable:initiator="initiator" flowable:formFieldValidation="true"></startEvent>
    <scriptTask id="firstScript" scriptFormat="JavaScript" flowable:autoStoreVariables="true">
      <script>
          <![CDATA[
              var decision = "[{'deci':'初审通过'},{'deci':'初审驳回'}]";
              execution.setVariable("decision",decision);
          ]]>
      </script>
    </scriptTask>
    <sequenceFlow id="sid-33507405-3B5F-4878-AF9A-5A5975D05B71" sourceRef="sid-C59BF991-D3C1-4F5C-8DED-CE6453589226" targetRef="firstScript"></sequenceFlow>
    <userTask id="firstAudit" name="初审" flowable:candidateGroups="1196771445488107565" flowable:formFieldValidation="true"></userTask>
    <exclusiveGateway id="sid-F1C3D382-DCB8-4A4E-8732-62D368BD0564"></exclusiveGateway>
    <scriptTask id="secondScript" scriptFormat="JavaScript" flowable:autoStoreVariables="true">
      <script>
        <![CDATA[
            var decision = "[{'deci':'二审通过'},{'deci':'二审驳回'}]";
            execution.setVariable("decision",decision);
        ]]>
      </script>
    </scriptTask>
    <userTask id="secondAudit" name="二审" flowable:candidateGroups="1196771445488107574" flowable:formFieldValidation="true"></userTask>
    <exclusiveGateway id="sid-D139A4E3-0CEA-46B6-895B-CE4ACBFAD5E3"></exclusiveGateway>
    <sequenceFlow id="sid-646339FE-D71E-4870-9467-DBF637F7E8F3" sourceRef="secondAudit" targetRef="sid-D139A4E3-0CEA-46B6-895B-CE4ACBFAD5E3"></sequenceFlow>
    <scriptTask id="finalScript" scriptFormat="JavaScript" flowable:autoStoreVariables="true">
      <script>
        <![CDATA[
            var decision = "[{'deci':'终审通过'},{'deci':'终审驳回'}]";
            execution.setVariable("decision",decision);
        ]]>
      </script>
    </scriptTask>
    <userTask id="finalAudit" name="终审"  flowable:candidateGroups="1196771445488107555"  flowable:formFieldValidation="true"></userTask>
    <scriptTask id="updateState" flowable:autoStoreVariables="false" scriptFormat="groovy">
      <script>
          <![CDATA[
                import com.dh.workflow.Client.BSH;
                import org.flowable.engine.runtime.ProcessInstance;
                def processInstanceId = execution.getVariable("processInstanceId");
                ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
                def businessKey = processInstance.getBusinessKey();
                if("终审通过".equals(deci)){
                    execution.setVariable("state",2);
                    BSH.sendRequest("crew","updateHoldTimeReport","businessKey="+businessKey+"#state=2");
                }else{
                    execution.setVariable("state",3);
                    BSH.sendRequest("crew","updateHoldTimeReport","businessKey="+businessKey+"#state=3");
                }
          ]]>
      </script>
    </scriptTask>
    <sequenceFlow id="sid-0435E99F-EF67-43EB-BF86-36D61E3A750D" sourceRef="secondScript" targetRef="secondAudit"></sequenceFlow>
    <sequenceFlow id="sid-9147CA73-34A2-4CDA-AE3F-02BAA78DBDB2" sourceRef="finalScript" targetRef="finalAudit"></sequenceFlow>
    <endEvent id="sid-5B059F2F-CB89-4C5E-8FC2-ABFF4DEBB573"></endEvent>
    <sequenceFlow id="sid-EC7733BA-B917-4F63-B227-B31ABEEE172A" sourceRef="firstAudit" targetRef="sid-F1C3D382-DCB8-4A4E-8732-62D368BD0564"></sequenceFlow>
    <sequenceFlow id="sid-B0142C11-F1C7-4EDC-8CC5-F79081042054" sourceRef="updateState" targetRef="sid-5B059F2F-CB89-4C5E-8FC2-ABFF4DEBB573"></sequenceFlow>
    <sequenceFlow id="sid-91D25989-FABC-43F5-86FD-DE5A49A3D082" sourceRef="sid-D139A4E3-0CEA-46B6-895B-CE4ACBFAD5E3" targetRef="finalScript">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${deci=='二审通过'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-7E31630F-92F4-4055-A286-AA50CA9EE732" sourceRef="firstScript" targetRef="firstAudit"></sequenceFlow>
    <sequenceFlow id="sid-201724D2-3E78-4F5D-93F6-E0C520641D28" sourceRef="sid-F1C3D382-DCB8-4A4E-8732-62D368BD0564" targetRef="secondScript">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${deci=='初审通过'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-B29A7EE6-E1B3-408A-903C-68FBCD0C90E7" sourceRef="sid-D139A4E3-0CEA-46B6-895B-CE4ACBFAD5E3" targetRef="updateState">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${deci=='二审驳回'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-18B2A7A4-6808-46B3-B5EC-6BFD8E21E42E" sourceRef="finalAudit" targetRef="updateState"></sequenceFlow>
    <sequenceFlow id="sid-FD69C765-F3BC-4F78-87EF-0F4DCC26FA00" sourceRef="sid-F1C3D382-DCB8-4A4E-8732-62D368BD0564" targetRef="updateState">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${deci=='初审驳回'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_holdTimeReport">
    <bpmndi:BPMNPlane bpmnElement="holdTimeReport" id="BPMNPlane_holdTimeReport">
      <bpmndi:BPMNShape bpmnElement="sid-C59BF991-D3C1-4F5C-8DED-CE6453589226" id="BPMNShape_sid-C59BF991-D3C1-4F5C-8DED-CE6453589226">
        <omgdc:Bounds height="30.0" width="30.0" x="56.37891201110318" y="86.0000014901161"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="firstScript" id="BPMNShape_firstScript">
        <omgdc:Bounds height="80.00000000000001" width="100.0" x="146.37891201110318" y="61.000001490116105"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="firstAudit" id="BPMNShape_firstAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="291.3789120111032" y="61.00000149011612"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-F1C3D382-DCB8-4A4E-8732-62D368BD0564" id="BPMNShape_sid-F1C3D382-DCB8-4A4E-8732-62D368BD0564">
        <omgdc:Bounds height="40.0" width="40.0" x="321.3789120111032" y="195.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="secondScript" id="BPMNShape_secondScript">
        <omgdc:Bounds height="80.0" width="100.0" x="420.0" y="60.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="secondAudit" id="BPMNShape_secondAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="570.0" y="60.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-D139A4E3-0CEA-46B6-895B-CE4ACBFAD5E3" id="BPMNShape_sid-D139A4E3-0CEA-46B6-895B-CE4ACBFAD5E3">
        <omgdc:Bounds height="40.0" width="40.0" x="600.0" y="195.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="finalScript" id="BPMNShape_finalScript">
        <omgdc:Bounds height="80.0" width="100.0" x="720.0" y="60.00000149011612"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="finalAudit" id="BPMNShape_finalAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="870.0" y="60.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="updateState" id="BPMNShape_updateState">
        <omgdc:Bounds height="80.0" width="100.0" x="1020.0" y="60.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-5B059F2F-CB89-4C5E-8FC2-ABFF4DEBB573" id="BPMNShape_sid-5B059F2F-CB89-4C5E-8FC2-ABFF4DEBB573">
        <omgdc:Bounds height="28.0" width="28.0" x="1170.0" y="87.00000149011612"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-201724D2-3E78-4F5D-93F6-E0C520641D28" id="BPMNEdge_sid-201724D2-3E78-4F5D-93F6-E0C520641D28">
        <omgdi:waypoint x="360.82152243093515" y="215.5"></omgdi:waypoint>
        <omgdi:waypoint x="470.0" y="215.5"></omgdi:waypoint>
        <omgdi:waypoint x="470.0" y="139.95"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-0435E99F-EF67-43EB-BF86-36D61E3A750D" id="BPMNEdge_sid-0435E99F-EF67-43EB-BF86-36D61E3A750D">
        <omgdi:waypoint x="519.95" y="100.0"></omgdi:waypoint>
        <omgdi:waypoint x="570.0" y="100.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-91D25989-FABC-43F5-86FD-DE5A49A3D082" id="BPMNEdge_sid-91D25989-FABC-43F5-86FD-DE5A49A3D082">
        <omgdi:waypoint x="639.4436664437862" y="215.5"></omgdi:waypoint>
        <omgdi:waypoint x="770.0" y="215.5"></omgdi:waypoint>
        <omgdi:waypoint x="770.0" y="139.9500014901161"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-7E31630F-92F4-4055-A286-AA50CA9EE732" id="BPMNEdge_sid-7E31630F-92F4-4055-A286-AA50CA9EE732">
        <omgdi:waypoint x="246.3289120111032" y="101.00000149011612"></omgdi:waypoint>
        <omgdi:waypoint x="291.3789120111032" y="101.00000149011612"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-B0142C11-F1C7-4EDC-8CC5-F79081042054" id="BPMNEdge_sid-B0142C11-F1C7-4EDC-8CC5-F79081042054">
        <omgdi:waypoint x="1119.95" y="100.43815854764298"></omgdi:waypoint>
        <omgdi:waypoint x="1170.000220139595" y="100.87719891681749"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-B29A7EE6-E1B3-408A-903C-68FBCD0C90E7" id="BPMNEdge_sid-B29A7EE6-E1B3-408A-903C-68FBCD0C90E7">
        <omgdi:waypoint x="620.5" y="234.4266625615764"></omgdi:waypoint>
        <omgdi:waypoint x="620.5" y="256.0"></omgdi:waypoint>
        <omgdi:waypoint x="1045.0" y="256.0"></omgdi:waypoint>
        <omgdi:waypoint x="1045.0" y="139.95"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-33507405-3B5F-4878-AF9A-5A5975D05B71" id="BPMNEdge_sid-33507405-3B5F-4878-AF9A-5A5975D05B71">
        <omgdi:waypoint x="86.32891084159621" y="101.0000014901161"></omgdi:waypoint>
        <omgdi:waypoint x="146.37891201110318" y="101.00000149011612"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-646339FE-D71E-4870-9467-DBF637F7E8F3" id="BPMNEdge_sid-646339FE-D71E-4870-9467-DBF637F7E8F3">
        <omgdi:waypoint x="620.1729437229437" y="139.95"></omgdi:waypoint>
        <omgdi:waypoint x="620.4130434782609" y="195.41304347826087"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-18B2A7A4-6808-46B3-B5EC-6BFD8E21E42E" id="BPMNEdge_sid-18B2A7A4-6808-46B3-B5EC-6BFD8E21E42E">
        <omgdi:waypoint x="969.9499999999999" y="100.0"></omgdi:waypoint>
        <omgdi:waypoint x="1020.0" y="100.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-9147CA73-34A2-4CDA-AE3F-02BAA78DBDB2" id="BPMNEdge_sid-9147CA73-34A2-4CDA-AE3F-02BAA78DBDB2">
        <omgdi:waypoint x="819.949999999993" y="100.00000099341075"></omgdi:waypoint>
        <omgdi:waypoint x="869.9999999999965" y="100.00000049620867"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-EC7733BA-B917-4F63-B227-B31ABEEE172A" id="BPMNEdge_sid-EC7733BA-B917-4F63-B227-B31ABEEE172A">
        <omgdi:waypoint x="341.5533661618452" y="140.9500014901161"></omgdi:waypoint>
        <omgdi:waypoint x="341.79119271171095" y="195.4122807006078"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-FD69C765-F3BC-4F78-87EF-0F4DCC26FA00" id="BPMNEdge_sid-FD69C765-F3BC-4F78-87EF-0F4DCC26FA00">
        <omgdi:waypoint x="341.8789120111032" y="234.44180363321794"></omgdi:waypoint>
        <omgdi:waypoint x="341.8789120111032" y="331.0"></omgdi:waypoint>
        <omgdi:waypoint x="1070.0" y="331.0"></omgdi:waypoint>
        <omgdi:waypoint x="1070.0" y="139.95"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>