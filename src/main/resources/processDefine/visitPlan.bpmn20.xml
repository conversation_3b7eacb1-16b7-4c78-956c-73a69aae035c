<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="visitPlan" name="年度访船计划" isExecutable="true">
    <documentation>年度访船计划</documentation>
    <startEvent id="sid-29A1A18C-7785-4B30-8D52-2D0FE16D17EA" flowable:initiator="initiator" flowable:formKey="visitPlanForm" flowable:formFieldValidation="true"></startEvent>
    <scriptTask id="firstScript" scriptFormat="JavaScript" flowable:autoStoreVariables="true">
      <script><![CDATA[var decision = "[{'deci':'初审通过'},{'deci':'初审驳回'}]";
                    execution.setVariable("decision",decision);]]></script>
    </scriptTask>
    <sequenceFlow id="sid-E4F273C9-D4CA-4EC1-8909-11817DF1A6BD" sourceRef="sid-29A1A18C-7785-4B30-8D52-2D0FE16D17EA" targetRef="firstScript"></sequenceFlow>
    <userTask id="firstAudit" name="初审">
      <extensionElements>
        <flowable:taskListener event="create" class="com.dh.workflow.handler.SubmitByLeaderHandler"></flowable:taskListener>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-AB270E41-BBEE-4D51-8FEC-8CA0B658FB92" sourceRef="firstScript" targetRef="firstAudit"></sequenceFlow>
    <scriptTask id="finalScript" scriptFormat="JavaScript" flowable:autoStoreVariables="true">
      <script><![CDATA[var decision = "[{'deci':'终审通过'},{'deci':'终审驳回'}]";
                    execution.setVariable("decision",decision);]]></script>
    </scriptTask>
    <userTask id="finalAudit" name="终审">
      <extensionElements>
        <flowable:taskListener event="create" class="com.dh.workflow.handler.SubmitByLeaderHandler"></flowable:taskListener>
      </extensionElements>
    </userTask>
    <scriptTask id="updateState" scriptFormat="groovy" flowable:autoStoreVariables="false">
      <script><![CDATA[import com.dh.workflow.Client.BSH;
                    import org.flowable.engine.runtime.ProcessInstance;
                    def processInstanceId = execution.getVariable("processInstanceId");
                    ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
                    def businessKey = processInstance.getBusinessKey();
                    if("终审通过".equals(deci)){
                        execution.setVariable("state",2);
                        BSH.sendRequest("crew","updateVisitPlan","businessKey="+businessKey+"#state=2");
                    }else{
                        execution.setVariable("state",3);
                        BSH.sendRequest("crew","updateVisitPlan","businessKey="+businessKey+"#state=3");
                    }]]></script>
    </scriptTask>
    <sequenceFlow id="sid-23CFE4AE-7794-4B7A-8F70-65054DAC1B96" sourceRef="finalScript" targetRef="finalAudit"></sequenceFlow>
    <exclusiveGateway id="sid-44CBB98F-7833-4668-B4E2-E42C4B9B68D6"></exclusiveGateway>
    <sequenceFlow id="sid-3A63A1F0-062C-45C6-B38E-897C74771D5B" sourceRef="finalAudit" targetRef="updateState"></sequenceFlow>
    <endEvent id="sid-3A15625C-43D2-4CA3-B706-4E9197DE27F5"></endEvent>
    <sequenceFlow id="sid-FE686A10-2695-4D54-9B4A-6613F8F63C03" sourceRef="firstAudit" targetRef="sid-44CBB98F-7833-4668-B4E2-E42C4B9B68D6"></sequenceFlow>
    <sequenceFlow id="sid-75F548D7-6934-4EAC-A33E-75A48F4D5538" sourceRef="updateState" targetRef="sid-3A15625C-43D2-4CA3-B706-4E9197DE27F5"></sequenceFlow>
    <sequenceFlow id="sid-AC5BECD8-B60A-4081-89B1-1E225AFA2424" sourceRef="sid-44CBB98F-7833-4668-B4E2-E42C4B9B68D6" targetRef="updateState">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${deci=='初审驳回'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-31D0C2DA-D67F-4080-AFF9-A55AB4972B3F" sourceRef="sid-44CBB98F-7833-4668-B4E2-E42C4B9B68D6" targetRef="finalScript">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${deci=='初审通过'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_visitPlan">
    <bpmndi:BPMNPlane bpmnElement="visitPlan" id="BPMNPlane_visitPlan">
      <bpmndi:BPMNShape bpmnElement="sid-29A1A18C-7785-4B30-8D52-2D0FE16D17EA" id="BPMNShape_sid-29A1A18C-7785-4B30-8D52-2D0FE16D17EA">
        <omgdc:Bounds height="30.0" width="30.0" x="135.0" y="130.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="firstScript" id="BPMNShape_firstScript">
        <omgdc:Bounds height="80.0" width="100.0" x="240.0" y="105.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="firstAudit" id="BPMNShape_firstAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="375.0" y="105.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="finalScript" id="BPMNShape_finalScript">
        <omgdc:Bounds height="80.0" width="100.0" x="540.0" y="111.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="finalAudit" id="BPMNShape_finalAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="675.0" y="111.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="updateState" id="BPMNShape_updateState">
        <omgdc:Bounds height="80.0" width="100.0" x="840.0" y="111.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-44CBB98F-7833-4668-B4E2-E42C4B9B68D6" id="BPMNShape_sid-44CBB98F-7833-4668-B4E2-E42C4B9B68D6">
        <omgdc:Bounds height="40.0" width="40.0" x="405.0" y="240.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3A15625C-43D2-4CA3-B706-4E9197DE27F5" id="BPMNShape_sid-3A15625C-43D2-4CA3-B706-4E9197DE27F5">
        <omgdc:Bounds height="28.0" width="28.0" x="1020.0" y="137.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-AB270E41-BBEE-4D51-8FEC-8CA0B658FB92" id="BPMNEdge_sid-AB270E41-BBEE-4D51-8FEC-8CA0B658FB92">
        <omgdi:waypoint x="339.95000000000005" y="145.0"></omgdi:waypoint>
        <omgdi:waypoint x="374.99999999997203" y="145.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-3A63A1F0-062C-45C6-B38E-897C74771D5B" id="BPMNEdge_sid-3A63A1F0-062C-45C6-B38E-897C74771D5B">
        <omgdi:waypoint x="774.9499999999836" y="151.0"></omgdi:waypoint>
        <omgdi:waypoint x="839.9999999998909" y="151.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-75F548D7-6934-4EAC-A33E-75A48F4D5538" id="BPMNEdge_sid-75F548D7-6934-4EAC-A33E-75A48F4D5538">
        <omgdi:waypoint x="939.9499999999827" y="151.0"></omgdi:waypoint>
        <omgdi:waypoint x="1020.0" y="151.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-FE686A10-2695-4D54-9B4A-6613F8F63C03" id="BPMNEdge_sid-FE686A10-2695-4D54-9B4A-6613F8F63C03">
        <omgdi:waypoint x="425.0062091503268" y="184.95"></omgdi:waypoint>
        <omgdi:waypoint x="425.36842105263156" y="240.3684210526316"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-31D0C2DA-D67F-4080-AFF9-A55AB4972B3F" id="BPMNEdge_sid-31D0C2DA-D67F-4080-AFF9-A55AB4972B3F">
        <omgdi:waypoint x="443.94836201972674" y="261.0"></omgdi:waypoint>
        <omgdi:waypoint x="590.0" y="261.0"></omgdi:waypoint>
        <omgdi:waypoint x="590.0" y="190.95"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-23CFE4AE-7794-4B7A-8F70-65054DAC1B96" id="BPMNEdge_sid-23CFE4AE-7794-4B7A-8F70-65054DAC1B96">
        <omgdi:waypoint x="639.9499999999999" y="151.0"></omgdi:waypoint>
        <omgdi:waypoint x="675.0" y="151.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-E4F273C9-D4CA-4EC1-8909-11817DF1A6BD" id="BPMNEdge_sid-E4F273C9-D4CA-4EC1-8909-11817DF1A6BD">
        <omgdi:waypoint x="164.94999906759472" y="145.0"></omgdi:waypoint>
        <omgdi:waypoint x="239.9999999998886" y="145.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-AC5BECD8-B60A-4081-89B1-1E225AFA2424" id="BPMNEdge_sid-AC5BECD8-B60A-4081-89B1-1E225AFA2424">
        <omgdi:waypoint x="425.7971234521233" y="279.13691502175135"></omgdi:waypoint>
        <omgdi:waypoint x="426.3999938964844" y="317.0"></omgdi:waypoint>
        <omgdi:waypoint x="890.0" y="317.0"></omgdi:waypoint>
        <omgdi:waypoint x="890.0" y="190.95"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
