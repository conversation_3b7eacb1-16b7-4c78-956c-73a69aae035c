<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="visitReport" name="访船报告" isExecutable="true">
    <documentation>访船报告工作流</documentation>
    <startEvent id="startEvent1" flowable:initiator="initiator" flowable:formKey="visitReportForm" flowable:formFieldValidation="true"></startEvent>
    <scriptTask id="firstScript" scriptFormat="JavaScript" flowable:autoStoreVariables="true">
      <script><![CDATA[var decision = "[{'deci':'初审通过'},{'deci':'初审驳回'}]";
                    execution.setVariable("decision",decision);]]></script>
    </scriptTask>
    <sequenceFlow id="sid-7DAD5D6D-9B26-4604-8F6B-D06F1B3FD8D4" sourceRef="startEvent1" targetRef="firstScript"></sequenceFlow>
    <userTask id="firstAudit" name="初审">
      <extensionElements>
        <flowable:taskListener event="create" class="com.dh.workflow.handler.SubmitByLeaderHandler"></flowable:taskListener>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-C1F68B01-B29F-4E8E-A6B0-95142F12A0AF" sourceRef="firstScript" targetRef="firstAudit"></sequenceFlow>
    <exclusiveGateway id="sid-715759F0-BB5F-42D0-96F7-7BD910187734"></exclusiveGateway>
    <sequenceFlow id="sid-C07B0EA7-51A4-4F5D-8A5D-5DA494B79D43" sourceRef="firstAudit" targetRef="sid-715759F0-BB5F-42D0-96F7-7BD910187734"></sequenceFlow>
    <scriptTask id="finalScript" scriptFormat="JavaScript" flowable:autoStoreVariables="true">
      <script><![CDATA[var decision = "[{'deci':'终审通过'},{'deci':'终审驳回'}]";
                    execution.setVariable("decision",decision);]]></script>
    </scriptTask>
    <userTask id="finalAudit" name="终审">
        <extensionElements>
            <flowable:taskListener event="create" class="com.dh.workflow.handler.SubmitByLeaderHandler"></flowable:taskListener>
        </extensionElements>
    </userTask>
    <sequenceFlow id="sid-8639B97D-C126-4CF1-9853-24FF4DD7E706" sourceRef="finalScript" targetRef="finalAudit"></sequenceFlow>
    <scriptTask id="updateState" scriptFormat="groovy" flowable:autoStoreVariables="false">
      <script><![CDATA[import com.dh.workflow.Client.BSH;
                    import org.flowable.engine.runtime.ProcessInstance;
                    def processInstanceId = execution.getVariable("processInstanceId");
                    ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
                    def businessKey = processInstance.getBusinessKey();
                    if("终审通过".equals(deci)){
                        execution.setVariable("reportState",2);
                        BSH.sendRequest("crew","updateVisitReport","businessKey="+businessKey+"#state=2");
                    }else{
                        execution.setVariable("reportState",3);
                        BSH.sendRequest("crew","updateVisitReport","businessKey="+businessKey+"#state=3");
                    }]]></script>
    </scriptTask>
    <sequenceFlow id="sid-04D329CD-C476-48B7-9E54-F83576335713" sourceRef="finalAudit" targetRef="updateState"></sequenceFlow>
    <endEvent id="sid-E1789650-4B65-4A86-8839-65450B9C39DC"></endEvent>
    <sequenceFlow id="sid-D35B2E4B-A5D9-487C-B264-5BABC59E539D" sourceRef="updateState" targetRef="sid-E1789650-4B65-4A86-8839-65450B9C39DC"></sequenceFlow>
    <sequenceFlow id="sid-4BC81E42-9413-4E37-B5E3-A5E148411691" sourceRef="sid-715759F0-BB5F-42D0-96F7-7BD910187734" targetRef="finalScript">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${deci=='初审通过'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-102B5BCB-5AFC-47B2-84A1-28490DA6FA8E" sourceRef="sid-715759F0-BB5F-42D0-96F7-7BD910187734" targetRef="updateState">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${deci=='初审驳回'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_visitReport">
    <bpmndi:BPMNPlane bpmnElement="visitReport" id="BPMNPlane_visitReport">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="75.0" y="150.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="firstScript" id="BPMNShape_firstScript">
        <omgdc:Bounds height="80.0" width="100.0" x="165.0" y="125.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="firstAudit" id="BPMNShape_firstAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="339.4" y="125.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-715759F0-BB5F-42D0-96F7-7BD910187734" id="BPMNShape_sid-715759F0-BB5F-42D0-96F7-7BD910187734">
        <omgdc:Bounds height="40.0" width="40.0" x="369.4" y="255.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="finalScript" id="BPMNShape_finalScript">
        <omgdc:Bounds height="80.0" width="100.0" x="585.0" y="125.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="finalAudit" id="BPMNShape_finalAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="730.0" y="125.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="updateState" id="BPMNShape_updateState">
        <omgdc:Bounds height="80.0" width="100.0" x="900.0" y="125.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-E1789650-4B65-4A86-8839-65450B9C39DC" id="BPMNShape_sid-E1789650-4B65-4A86-8839-65450B9C39DC">
        <omgdc:Bounds height="28.0" width="28.0" x="1045.0" y="151.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-C07B0EA7-51A4-4F5D-8A5D-5DA494B79D43" id="BPMNEdge_sid-C07B0EA7-51A4-4F5D-8A5D-5DA494B79D43">
        <omgdi:waypoint x="389.5807692307692" y="204.95"></omgdi:waypoint>
        <omgdi:waypoint x="389.80909090909086" y="255.4090909090909"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-4BC81E42-9413-4E37-B5E3-A5E148411691" id="BPMNEdge_sid-4BC81E42-9413-4E37-B5E3-A5E148411691">
        <omgdi:waypoint x="408.846135807504" y="275.5"></omgdi:waypoint>
        <omgdi:waypoint x="635.0" y="275.5"></omgdi:waypoint>
        <omgdi:waypoint x="635.0" y="204.95"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C1F68B01-B29F-4E8E-A6B0-95142F12A0AF" id="BPMNEdge_sid-C1F68B01-B29F-4E8E-A6B0-95142F12A0AF">
        <omgdi:waypoint x="264.9499999997882" y="165.0"></omgdi:waypoint>
        <omgdi:waypoint x="339.39999999990044" y="165.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-102B5BCB-5AFC-47B2-84A1-28490DA6FA8E" id="BPMNEdge_sid-102B5BCB-5AFC-47B2-84A1-28490DA6FA8E">
        <omgdi:waypoint x="389.9" y="294.4355564024391"></omgdi:waypoint>
        <omgdi:waypoint x="389.9" y="341.0"></omgdi:waypoint>
        <omgdi:waypoint x="950.0" y="341.0"></omgdi:waypoint>
        <omgdi:waypoint x="950.0" y="204.95"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-7DAD5D6D-9B26-4604-8F6B-D06F1B3FD8D4" id="BPMNEdge_sid-7DAD5D6D-9B26-4604-8F6B-D06F1B3FD8D4">
        <omgdi:waypoint x="104.94999883049303" y="165.0"></omgdi:waypoint>
        <omgdi:waypoint x="165.0" y="165.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8639B97D-C126-4CF1-9853-24FF4DD7E706" id="BPMNEdge_sid-8639B97D-C126-4CF1-9853-24FF4DD7E706">
        <omgdi:waypoint x="684.9499999999907" y="165.0"></omgdi:waypoint>
        <omgdi:waypoint x="729.9999999999807" y="165.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-D35B2E4B-A5D9-487C-B264-5BABC59E539D" id="BPMNEdge_sid-D35B2E4B-A5D9-487C-B264-5BABC59E539D">
        <omgdi:waypoint x="999.949999999996" y="165.0"></omgdi:waypoint>
        <omgdi:waypoint x="1045.0" y="165.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-04D329CD-C476-48B7-9E54-F83576335713" id="BPMNEdge_sid-04D329CD-C476-48B7-9E54-F83576335713">
        <omgdi:waypoint x="829.9499999999999" y="165.0"></omgdi:waypoint>
        <omgdi:waypoint x="900.0" y="165.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
