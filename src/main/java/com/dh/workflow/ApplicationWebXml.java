package com.dh.workflow;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

import java.util.HashMap;
import java.util.Map;

/**
 * java帮助类，提供一种替代创建web.xml的方式
 * 仅在应用发布到web容器时才会执行，例如：Tomcat、JBoss等。
 */
public class ApplicationWebXml extends SpringBootServletInitializer {
    private static final String SPRING_PROFILE_DEFAULT = "spring.profiles.default";
    private static final String ACTIVE_PROFILE_DEFAULT = "dev";

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        SpringApplication application = builder.application();

        // 设置默认配置文件
        Map<String, Object> defProperties = new HashMap<>();
        defProperties.put(SPRING_PROFILE_DEFAULT, ACTIVE_PROFILE_DEFAULT);
        application.setDefaultProperties(defProperties);

        return builder.sources(WorkFlowApplication.class);
    }
}
