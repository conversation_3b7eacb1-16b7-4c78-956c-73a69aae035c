package com.dh.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2020/7/8
 */
@Data
public class TaskDTO {

    @ApiModelProperty(value="任务id")
    private String taskId;

    @ApiModelProperty(value="任务名称")
    private String taskName;

    @ApiModelProperty(value="审批人")
    private String approver;

    @ApiModelProperty(value="审批人id")
    private String approverId;

    @ApiModelProperty(value = "流程发起人")
    private String originator;

    @ApiModelProperty(value = "流程发起时间")
    @DateTimeFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime originatorDate;

    @ApiModelProperty(value="业务主键")
    private String businessKey;

    @ApiModelProperty(value = "流程定义key")
    private String processDefinitionKey;

    @ApiModelProperty(value = "流程定义名称")
    private String processDefinitionName;

    @ApiModelProperty(value="流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value="开始时间")
    @DateTimeFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime ;

    @ApiModelProperty(value="结束时间")
    @DateTimeFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "审核意见")
    private String message;

    @ApiModelProperty(value = "审批类型")
    private String approveType;
}
