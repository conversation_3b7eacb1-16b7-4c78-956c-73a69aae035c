package com.dh.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020/7/8
 */
@Data
public class CommentDTO {

    @ApiModelProperty(value = "任务id")
    protected String taskId;

    @ApiModelProperty(value = "任务id")
    protected String userId;

    @ApiModelProperty(value = "用户的名称")
    protected String userName;

    @ApiModelProperty(value = "流程实例id")
    protected String processInstanceId;

    @ApiModelProperty(value = "意见")
    protected String message;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "评论")
    private String fullMsg;

    public CommentDTO(){}
    public CommentDTO(String userId, String processInstanceId, String type, String message) {
        this.userId = userId;
        this.processInstanceId = processInstanceId;
        this.message = message;
        this.type = type;
    }
    public CommentDTO(String taskId, String userId, String processInstanceId, String type, String message) {
        this.taskId = taskId;
        this.userId = userId;
        this.processInstanceId = processInstanceId;
        this.message = message;
        this.type = type;
    }

}
