package com.dh.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/7/7
 */
@Data
public class StartProcessInstanceDTO {

    @NotNull(message = "流程定义key不能为空")
    @ApiModelProperty(value = "流程定义key")
    private String processDefinitionKey;

    @NotNull(message="业务主键不能为空")
    @ApiModelProperty(value = "业务主键")
    private String businessKey;

    @ApiModelProperty(value = "启动流程变量")
    private Map<String,Object> variables;

    @ApiModelProperty(value = "启动表单变量")
    private Map<String,String> formVariables;

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "流程创建人")
    private String createUserId;

    @ApiModelProperty(value = "表单名称【名称+编码】")
    private String formName;
}
