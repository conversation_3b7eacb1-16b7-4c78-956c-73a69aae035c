package com.dh.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 流程执行过程中基本参数
 * <AUTHOR>
 * @since 2020/7/8
 */
@Data
public class BaseProcessDTO {

    @NotNull
    @ApiModelProperty(value = "任务id")
    private String taskId;


    @NotNull
    @ApiModelProperty(value = "审批意见")
    private String message;

    @NotNull
    @ApiModelProperty(value = "流程实例的id")
    private String processInstanceId;

    @ApiModelProperty(value = "审批类型(审批，驳回...)")
    private String type;

    private Map<String,Object> variables;
}
