package com.dh.workflow.dto;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/10
 */
@Data
@ApiModel(value = "工作流查询用户entity")
public class FlowableUserQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    private Long id;

    @ApiModelProperty(value = "用户id集合")
    private List<Long> ids;

    @ApiModelProperty(value ="用户名称")
    private String username;

    @ApiModelProperty(value = "用户名称模糊匹配")
    private String usernameLike;

    @ApiModelProperty(value = "组id")
    private Long groupId;

    @ApiModelProperty(value = "组id集合")
    private List<Long> groupIds;
}
