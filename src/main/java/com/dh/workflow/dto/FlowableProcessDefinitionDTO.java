package com.dh.workflow.dto;

import com.dh.workflow.entity.FileAnnex;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/16
 */
@Data
public class FlowableProcessDefinitionDTO {

    @ApiModelProperty(value = "主键id")
    private String id;

    @ApiModelProperty(value = "流程定义key")
    private String key;

    @ApiModelProperty(value = "流程定义名称")
    private String name;

    @ApiModelProperty(value = "流程定义版本")
    private int version;

    @ApiModelProperty(value = "流程定义的Namespace就是类别")
    private String category;

    @ApiModelProperty(value = "部署ID")
    private String deploymentId;

    @ApiModelProperty(value = "流程bpmn文件名称")
    private String resourceName;

    @ApiModelProperty(value = "流程图片名称\n")
    private String dgrmResourceName;

    @ApiModelProperty(value = "1 激活 2挂起")
    private int suspensionState;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "附件")
    private List<FileAnnex> fileAnnexList;


}
