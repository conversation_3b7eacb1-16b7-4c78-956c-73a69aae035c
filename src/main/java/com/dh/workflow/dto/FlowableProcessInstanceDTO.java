package com.dh.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020/7/8
 */
@Data
public class FlowableProcessInstanceDTO {

    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    @ApiModelProperty(value = "流程定义id")
    private String processDefinitionId;

    @ApiModelProperty(value = "激活状态 1激活 2挂起")
    private int suspensionState;

    @ApiModelProperty(value = "表单名称")
    private String formName;

    @ApiModelProperty(value = "业务主键")
    private String businessKey;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "审批人")
    private String approver;

    @ApiModelProperty(value = "发起人")
    private String starter;

    @ApiModelProperty(value = "发起人id")
    private String starterId;

    @ApiModelProperty(value = "系统标识")
    private String systemSn;

}
