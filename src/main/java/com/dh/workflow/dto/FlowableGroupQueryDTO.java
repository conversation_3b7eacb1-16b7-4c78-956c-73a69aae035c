package com.dh.workflow.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/10
 */
@Data
@ApiModel(value = "工作流查询组entity")
public class FlowableGroupQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组id")
    private Long id;

    @ApiModelProperty(value = "组id集合")
    private List<Long> ids;

    @ApiModelProperty(value="组名称")
    private String name;

    @ApiModelProperty(value ="组名称模糊匹配")
    private String namelike;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value="用户id集合")
    private List<Long> userIds;

}
