package com.dh.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 扩展信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("workflow_extinfo")
@Accessors(chain = true)
public class WorkflowExtinfo {

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "工作流id")
    private Long processId;

    @ApiModelProperty(value = "扩展id")
    private Long extId;

    @ApiModelProperty(value = "分类的标题")
    private String title;
}
