package com.dh.workflow.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2020-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "FileAnnex对象", description = "")
public class FileAnnex implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "file_annex_id", type = IdType.AUTO)
    private Long fileAnnexId;

    @ApiModelProperty(value = "上传的文件名")
    @TableField("file_Name")
    private String fileName;

    @ApiModelProperty(value = "原有文件名")
    @TableField("old_file_Name")
    private String oldFileName;

    @ApiModelProperty(value = "文件类型")
    @TableField("file_type")
    private String fileType;

    @ApiModelProperty(value = "上传时间")
    @TableField("upload_time")
    private LocalDateTime uploadTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_date", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateDate;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    private LocalDateTime createDate;

    @ApiModelProperty(value = "表Id")
    @TableField("table_id")
    private Long tableId;

    @ApiModelProperty(value = "业务类型，0标示题目附件,1，答题附件")
    @TableField("table_type")
    private Long tableType;

    @ApiModelProperty(value = "删除标识(0: 否，1:是)")
    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;

    @ApiModelProperty(value = "试卷ID")
    @TableField("paper_table_id")
    private Long paperTableId;

    @ApiModelProperty(value = "阿里oss文件夹")
    @TableField("oss_folder")
    private String ossFolder;

    @ApiModelProperty(value = "工作流主键id")
    @TableField("flowable_table_id")
    private String flowableTableId;


}
