package com.dh.workflow.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 消息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="MessageInfo对象", description="消息表")
public class MessageInfo implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ID_WORKER)
    private Long id;

    @ApiModelProperty(value = "消息类型（1:我的待办，2:我的通知，3:公司公告）")
    private Integer msgType;

    @ApiModelProperty(value = "消息主题")
    private String subject;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "消息跳转链接")
    private String msgUrl;

    @ApiModelProperty(value = "业务主键id,用于更新待办状态")
    private String businessId;

    @ApiModelProperty(value = "业务类型（举例子1:初审，2：复审，10：执行力）")
    private String businessType;

    @ApiModelProperty(value = "创建人Id")
    private Long createBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createByName;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;

    @ApiModelProperty(value = "接收人id")
    private Long receiveTo;

    @ApiModelProperty(value = "是否已读（0：否，1：是）")
    private Integer readFlag;

    @ApiModelProperty(value = "是否处理（0：否，1：是）")
    private Integer handleFlag;

    @ApiModelProperty(value = "处理时间")
    private LocalDateTime handleDate;

    @ApiModelProperty(value = "处理截止时间（字符串）")
    private String deadlineDate;

    @ApiModelProperty(value = "是否弹窗(0否1是)")
    private Integer windowFlag;

    @ApiModelProperty(value = "参数集合(json格式)")
    private String paramsValue;

    @ApiModelProperty(value = "模板参数")
    private Long messageTemplateId;
}
