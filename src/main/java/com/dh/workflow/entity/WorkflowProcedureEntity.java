package com.dh.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("workflow_procedure")
@ApiModel(value = "工作流程")
@Accessors(chain = true)
public class WorkflowProcedureEntity implements Serializable {
    @ApiModelProperty(value = "工作流程ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("process_inst_id")
    private Long processInstId;

    @ApiModelProperty(value = "流程节点ID")
    @TableField("process_node_id")
    private Long processNodeId;


    @ApiModelProperty(value = "流程节点名称")
    @TableField("deadline_by")
    private Long deadlineBy;

    @ApiModelProperty(value = "流程节点名称")
    @TableField("current_process_users")
    private String currentProcessUsers;

    @ApiModelProperty(value = "流程处理时间")
    @TableField("deadline_date")
    private LocalDateTime deadlineDate;

    @ApiModelProperty(value = "节点处理意见")
    @TableField("deadline_suggestion")
    private String deadlineSuggestion;

    @ApiModelProperty(value = "处理结果 通过/拒绝/撤回")
    @TableField("deadline_result")
    private String deadlineResult;

    @ApiModelProperty(value = "开始时间")
    @TableField("start_date")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "结束时间")
    @TableField("end_date")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "扩展内容")
    private String extContent;
}