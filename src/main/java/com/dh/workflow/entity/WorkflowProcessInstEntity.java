package com.dh.workflow.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 流程实例表
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "流程实例表")
@Accessors(chain = true)
@TableName(value = "workflow_process_inst", autoResultMap = true)
public class WorkflowProcessInstEntity implements Serializable {
    @ApiModelProperty(value = "流程实例ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("business_id")
    private Long businessId;

    @TableField("process_id")
    private Long processId;

    @ApiModelProperty(value = "扩展ID")
    @TableField("ext_id")
    private Long extId;

    @ApiModelProperty(value = "状态Code")
    @TableField("status_code")
    private Integer statusCode;

    @ApiModelProperty(value = "状态内容")
    @TableField("status_text")
    private String statusText;

    @ApiModelProperty(value = "当前处理人")
    @TableField("deadline_by")
    private String deadlineBy;

    @ApiModelProperty(value = "创建人")
    @TableField("created_by")
    private Long createdBy;

    @ApiModelProperty(value = "当前处理步骤")
    @TableField("current_procedure_id")
    private Long currentProcedureId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value="created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value="updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @ApiModelProperty(value = "扩展参数")
    @TableField(value="params", typeHandler = JacksonTypeHandler.class)
    private Map<String, String> params;
}
