package com.dh.workflow.entity;

import lombok.Data;

import java.util.Date;

/**
 * 工作流Entity
 */
@Data
public class Flow {
	
	private static final long serialVersionUID = 1L;
	
	private String taskId; 		// 任务编号
	private String taskName; 	// 任务名称
	private String taskDefKey; 	// 任务定义Key（任务环节标识）
	private String assignee; // 任务执行人编号
	private String assigneeName; // 任务执行人名称
	
	private Date createTime;
	private Date endTime;
	
	private Date hisTaskEndTime; // 历史任务结束时间
	private String executionId;

	private String formId;      // 流程表单ID
	private String modelVersion; // 表单关联模型版本
	private String procInsId; 	// 流程实例ID
	private String hisProcInsId;//已办任务流程实例ID 查看流程图会用到
	private String processIsFinished ;//流程实例是否结束(true:结束，false:未结束)
	
	//ProcessDefinition
	private String procDefId; 	// 流程定义ID
	private String procDefKey;
	private String procDefname;
	private int procDefversion;
	private String proceDefDesc;
	private String proceDefDiagramResName;
	private String proceDefResName;
	
	// HistoricActivityInstance
	private String hisActInsActName;
	private String hisActInsDuTime; // 历史活动耗时

	
//	private String businessTable;	// 业务绑定Table
	private String businessId;		// 业务绑定ID

	private String status; 		// 任务状态
	private boolean hasUnAuditPassFlow; // 当前节点是否有驳回操作

	private String comment; 	// 任务意见
	private String flag; 		// 意见状态

//	private Variable vars; 		// 流程变量
//	private Variable taskVars; 	// 流程任务变量
	
	private Date beginDate;	// 开始查询日期
	private Date endDate;	// 结束查询日期

	private String fileIds; // 附件ID，以逗号分隔
	private String category; // 流程分类


}


