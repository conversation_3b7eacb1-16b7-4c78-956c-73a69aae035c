package com.dh.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 工作流 Entry
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("workflow_process")
@ApiModel(value = "工作流程")
@Accessors(chain = true)
public class WorkflowProcessEntity implements Serializable {
    @ApiModelProperty(value = "工作流程ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "版本号")
    private Integer version;

    @ApiModelProperty(value = "流程名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty(value = "流程key(唯一)")
    @TableField("`key`")
    private String key;

    @ApiModelProperty(value = "是否包含分组流程")
    private Integer isMulit;


    @ApiModelProperty(value = "流程hook")
    @TableField("hook_name")
    private String hookName;

    @ApiModelProperty(value = "是否开启Todo")
    @TableField("is_todo")
    private Integer isTodo;

    @ApiModelProperty(value = "Todo路由")
    @TableField("todo_route")
    private String todoRoute;
}
