package com.dh.workflow.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dh.common.constant.DateConstant;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 系统用户
 * </p>
 *
 * <AUTHOR> @since 2020-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SysUser对象", description="系统用户")
public class SysUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.ID_WORKER)
    private Long id;

    @ApiModelProperty(value = "用户名")
    @TableField("username")
    private String username;

    @ApiModelProperty(value = "密码")
    @TableField("password")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String password;

    @ApiModelProperty(value = "姓名")
    @TableField("real_name")
    private String realName;

    @ApiModelProperty(value = "头像")
    @TableField("head_url")
    private String headUrl;

    @ApiModelProperty(value = "性别   0：男   1：女    2：保密")
    @TableField("gender")
    private Integer gender;

    @ApiModelProperty(value = "邮箱")
    @TableField("email")
    private String email;

    @ApiModelProperty(value = "手机号")
    @TableField("mobile")
    private String mobile;

    @ApiModelProperty(value = "部门ID")
    @TableField("dept_id")
    private Long deptId;

    @ApiModelProperty(value = "超级管理员   0：否   1：是")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @TableField("super_admin")
    private Integer superAdmin;

    @ApiModelProperty(value = "租户管理员   0：否   1：是")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @TableField("super_tenant")
    private Integer superTenant;

    @ApiModelProperty(value = "状态  0：停用   1：正常")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "创建者")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private Long creator;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private LocalDateTime createDate;

    @ApiModelProperty(value = "更新者")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @TableField(value = "updater", fill = FieldFill.INSERT)
    private Long updater;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_date", fill = FieldFill.INSERT_UPDATE)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private LocalDateTime updateDate;

    @ApiModelProperty(value = "是否可用 0：可用  1：不可用")
    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;

    @ApiModelProperty(value = "岗位ID")
    @TableField("post_id")
    private Long postId;

    @ApiModelProperty(value = "国籍")
    @TableField("country")
    private String country;

    @ApiModelProperty(value = "民族")
    @TableField("nation")
    private String nation;

    @ApiModelProperty(value = "婚姻状况(0:否, 1:是)")
    @TableField("marital_status")
    private Integer maritalStatus;

    @ApiModelProperty(value = "证件类型")
    @TableField("card_type")
    private String cardType;

    @ApiModelProperty(value = "身份证号")
    @TableField("card_no")
    private String cardNo;

    @ApiModelProperty(value = "出生年月")
    @TableField("birth_date")
    @DateTimeFormat( pattern = DateConstant.DEFAULT_DATE_FORMAT)
    private LocalDate birthDate;

    @ApiModelProperty(value = "出生地")
    @TableField("birth_place")
    private String birthPlace;

    @ApiModelProperty(value = "毕业学校")
    @TableField("school")
    private String school;

    @ApiModelProperty(value = "毕业时间")
    @DateTimeFormat( pattern = DateConstant.DEFAULT_DATE_FORMAT)
    @TableField("graduation_date")
    private LocalDate graduationDate;

    @ApiModelProperty(value = "入校时间")
    @DateTimeFormat( pattern = DateConstant.DEFAULT_DATE_FORMAT)
    @TableField("enrollment_date")
    private LocalDate enrollmentDate;

    @ApiModelProperty(value = "学历")
    @TableField("education")
    private String education;

    @ApiModelProperty(value = "专业")
    @TableField("major")
    private String major;

    @ApiModelProperty(value = "英语能力")
    @TableField("english_level")
    private String englishLevel;

//    @ApiModelProperty(value = "身高,单位cm")
//    @TableField("height")
//    private Float height;
//
//    @ApiModelProperty(value = "体重,单位kg")
//    @TableField("weight")
//    private Float weight;
//
//    @ApiModelProperty(value = "血型")
//    @TableField("blood")
//    private String blood;
//
//    @ApiModelProperty(value = "鞋号")
//    @TableField("shoe_size")
//    private String shoeSize;
//
//    @ApiModelProperty(value = "慢性病")
//    @TableField("chronic_disease")
//    private String chronicDisease;

    @ApiModelProperty(value = "开户行")
    @TableField("bank")
    private String bank;

    @ApiModelProperty(value = "开户人")
    @TableField("holder")
    private String holder;

    @ApiModelProperty(value = "银行账号")
    @TableField("bank_account")
    private String bankAccount;

    @ApiModelProperty(value = "SWIFT CODE银行代码")
    @TableField("swift_code")
    private String swiftCode;

    @ApiModelProperty(value = "账号备注")
    @TableField("bank_note")
    private String bankNote;

    @ApiModelProperty(value = "社保缴纳地")
    @TableField("social_insurance_place")
    private String socialInsurancePlace;

    @ApiModelProperty(value = "社保缴纳开始时间")
    @TableField("social_insurance_begin_date")
    private LocalDate socialInsuranceBeginDate;

    @ApiModelProperty(value = "社保缴纳截止时间")
    @TableField("social_insurance_end_date")
    private LocalDate socialInsuranceEndDate;

    @ApiModelProperty(value = "微信号")
    @TableField("wx")
    private String wx;

    @ApiModelProperty(value = "英文名字")
    @TableField("english_name")
    private String englishName;

    @ApiModelProperty(value = "工号")
    @TableField("job_no")
    private String jobNo;

    @ApiModelProperty(value = "家庭联系方式")
    @TableField("family_mobile")
    private String familyMobile;

    @ApiModelProperty(value = "家庭住址")
    @TableField("address")
    private String address;

    @ApiModelProperty(value = "邮编")
    @TableField("post_code")
    private String postCode;

    @ApiModelProperty(value = "健康状况")
    @TableField("health_condition")
    private String healthCondition;

    @TableField(exist = false)
    private Long UserId;

    @TableField(exist = false)
    private Long responsibleBy;
}
