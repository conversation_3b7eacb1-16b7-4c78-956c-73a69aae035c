package com.dh.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 非点击消除代办URL配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "非点击消除代办URL配置表")
@TableName("dh_message_url")
public class MessageUrl implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "消息跳转链接")
    private String msgUrl;

    @ApiModelProperty(value = "跳转链接备注")
    private String urlRemarks;

}
