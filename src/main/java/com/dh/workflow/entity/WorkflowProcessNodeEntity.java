package com.dh.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * 工作流程节点
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("workflow_process_node")
@ApiModel(value = "工作流程节点")
@Accessors(chain = true)
public class WorkflowProcessNodeEntity {
    @ApiModelProperty(value = "工作流程节点ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "工作流程ID")
    @TableField("process_id")
    private Long processId;

    @ApiModelProperty(value = "扩展ID")
    @TableField("ext_id")
    private Integer extId;

    @ApiModelProperty(value = "节点名称")
    @TableField("node_name")
    private String nodeName;

    @ApiModelProperty(value = "节点处理人/岗位ID/...")
    @TableField("deadline_by")
    private Long deadlineBy;

    @ApiModelProperty(value = "岗位ID/...")
    @TableField("deadline_post")
    private Long deadlinePost;

    @ApiModelProperty(value = "节点类型")
    private Integer type;

    @ApiModelProperty(value = "状态Code")
    @TableField("status_code")
    private Integer statusCode;

    @ApiModelProperty(value = "状态内容")
    @TableField("status_text")
    private String statusText;

    @ApiModelProperty(value = "Hook routing key")
    @TableField("hook_name")
    private String hookName;

    @ApiModelProperty(value = "审批人id(多个人审批人用','拼接)")
    private String deadlineByUserIds;

    @ApiModelProperty(value = "是否是审批人配置(0：审批人配置，1：抄送人配置)")
    @TableField("is_approver_config")
    private Integer isApproverConfig;
}
