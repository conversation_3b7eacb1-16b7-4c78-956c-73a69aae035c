package com.dh.workflow.exception;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @project: reportexecutiontracking
 * @ClassName: CustomException
 * @author: Fei
 * @creat: 2021/1/7 9:58
 * 描述:自定义异常类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor     //当删除、修改返回值为0时，可以使用自定义异常
public class CustomException extends RuntimeException{
    private Integer code;
    private String msg;
}
