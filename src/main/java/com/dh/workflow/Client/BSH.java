//package com.dh.workflow.Client;
//
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.toolkit.StringUtils;
//import com.dh.common.util.R;
//import com.dh.workflow.config.DhPropertiesConfig;
//import com.dh.workflow.util.SecurityUtil;
//import com.dh.workflow.util.SpringContextUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.core.ParameterizedTypeReference;
//import org.springframework.http.*;
//import org.springframework.web.client.RestTemplate;
//
//import java.util.HashMap;
//import java.util.Map;
//
//
///**
// * <AUTHOR>
// * @since 2020/7/11
// */
//@Slf4j
//public class BSH {
//
//    private static DhPropertiesConfig dhProperties = SpringContextUtil.getBean("dhPropertiesConfig");
//
//    private static RestTemplate restTemplate = SpringContextUtil.getBean("restTemplate");
//
//
//
//
//    /**
//     * 调业务系统
//     * @param code  业务系统code
//     * @param targetMethod 业务系统方法
//     * @param params 参数格式 "a=1#b=2#c=3"
//     * @return
//     */
//    public static void sendRequest(String code,String targetMethod,String params){
//        String rootPath = dhProperties.getPlatfromUrl()+code;
//        Map<String,Object> paramMap = new HashMap<>();
//        paramMap.put("reqCode",targetMethod);
//        String[] paramsArr = params.split("#");
//        if(!StringUtils.isEmpty(params)){
//            for(int i=0;i<paramsArr.length;i++){
//                paramMap.put(paramsArr[i].split("=")[0].toString(),paramsArr[i].split("=")[1]);
//            }
//        }
//        JSONObject json = new JSONObject(paramMap);
//        String jsonStr = json.toJSONString();
//        String requestUrl = rootPath+"/wfRemote/callService";
//
//        HttpEntity<String> httpEntity = new HttpEntity<>(jsonStr);
//        ResponseEntity<R<String>> responseEntity = restTemplate.exchange(requestUrl+"?token=" + SecurityUtil.getToken()
//                , HttpMethod.POST
//                ,httpEntity
//                , new ParameterizedTypeReference<R<String>>(){});
//        R<String> result = responseEntity.getBody();
//        if(result != null && "0".equals(result.getCode())) {
//            log.info("调业务系统接口url{},返回结果{}",requestUrl,result.getData());
//        }
//    }
//
//}
