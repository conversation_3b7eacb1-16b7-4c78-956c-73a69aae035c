package com.dh.workflow.bean.fm;

import com.dh.workflow.entity.WorkflowExtinfo;
import com.dh.workflow.entity.WorkflowProcessNodeEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 工作流程添加修改参数
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Data
public class WorkflowProcessFM {

    /**
     * 工作流程ID
     */
    @ApiModelProperty(value = "工作流程ID")
    private Long id;

    /**
     * 流程名称
     */
    @ApiModelProperty(value = "流程名称")
    private String name;

    /**
     * 流程key(唯一)
     */
    @ApiModelProperty(value = "流程key(唯一)")
    private String key;

    /**
     * 是否包含分组流程
     */
    @ApiModelProperty(value = "是否包含分组流程")
    private Integer isMulit;

    /**
     * 流程 完成/拒绝/撤回时触发Hook
     */
    @ApiModelProperty(value = "流程 完成/拒绝/撤回时触发Hook")
    private String hookName;

    /**
     * 是否开启Todo
     */
    @ApiModelProperty(value = "是否开启Todo")
    private Integer isTodo;

    /**
     * Todo的Route
     */
    @ApiModelProperty(value = "Todo的Route")
    private String todoRoute;

}
