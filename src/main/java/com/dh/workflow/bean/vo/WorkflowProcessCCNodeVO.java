package com.dh.workflow.bean.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * 工作流程节点，抄送人节点返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("工作流程节点，抄送人节点返回对象")
public class WorkflowProcessCCNodeVO {

    /**
     * 工作流程ID
     */
    @ApiModelProperty(value = "工作流程ID")
    private Long processId;

    /**
     * 扩展ID
     */
    @ApiModelProperty(value = "扩展ID")
    private Integer extId;

    /**
     * 节点名称
     */
    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    /**
     * 是否是审批人配置(0：审批人配置，1：抄送人配置)
     */
    @ApiModelProperty(value = "是否是审批人配置(0：审批人配置，1：抄送人配置)")
    private Integer isApproverConfig;

    /**
     * 抄送人配置列表
     */
    @ApiModelProperty(value = "抄送人配置列表")
    private List<CCConfigVO> nodeList;
}
