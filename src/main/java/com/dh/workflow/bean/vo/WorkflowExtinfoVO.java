package com.dh.workflow.bean.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 工作流程扩展信息返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("工作流程扩展信息返回对象")
public class WorkflowExtinfoVO {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 工作流id
     */
    @ApiModelProperty(value = "工作流id")
    private Long processId;

    /**
     * 扩展id
     */
    @ApiModelProperty(value = "扩展id")
    private Long extId;

    /**
     * 分类的标题
     */
    @ApiModelProperty(value = "分类的标题")
    private String title;
}
