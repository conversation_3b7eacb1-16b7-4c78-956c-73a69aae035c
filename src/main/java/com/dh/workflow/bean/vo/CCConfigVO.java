package com.dh.workflow.bean.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 抄送人节点配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("抄送人节点配置")
public class CCConfigVO {

    /**
     * 抄送人节点类型
     */
    @ApiModelProperty(value = "抄送人节点类型")
    private Integer type;

    /**
     * 节点处理人/岗位ID/...
     */
    @ApiModelProperty(value = "节点处理人/岗位ID/...")
    private Long deadlineBy;

    /**
     * 节点处理岗位ID
     */
    @ApiModelProperty(value = "节点处理岗位ID")
    private Long deadlinePost;

    /**
     * 审批人id(多个人审批人用','拼接)
     */
    @ApiModelProperty(value = "审批人id(多个人审批人用','拼接)")
    private String deadlineByUserIds;
}
