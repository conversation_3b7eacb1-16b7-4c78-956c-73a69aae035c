package com.dh.workflow.bean.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 查询流程实例列表返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiOperation("查询流程实例列表返回对象")
public class WorkflowProcessVO {

    /**
     * 工作流程ID
     */
    @ApiModelProperty(value = "工作流程ID")
    private Long id;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer version;

    /**
     * 流程名称
     */
    @ApiModelProperty(value = "流程名称")
    private String name;

    /**
     * 流程key(唯一)
     */
    @ApiModelProperty(value = "流程key(唯一)")
    private String key;

    /**
     * 是否包含分组流程
     */
    @ApiModelProperty(value = "是否包含分组流程")
    private Integer isMulit;

    /**
     * 流程 完成/拒绝/撤回时触发Hook
     */
    @ApiModelProperty(value = "流程 完成/拒绝/撤回时触发Hook")
    private String hookName;

    /**
     * 是否开启Todo
     */
    @ApiModelProperty(value = "是否开启Todo")
    private Integer isTodo;

    /**
     * Todo的Route
     */
    @ApiModelProperty(value = "Todo的Route")
    private String todoRoute;

}
