package com.dh.workflow.bean.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 工作流程节点返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("工作流程节点返回对象")
public class WorkflowProcessNodeVO {

    /**
     * 工作流程节点ID
     */
    @ApiModelProperty(value = "工作流程节点ID")
    private Long id;

    /**
     * 工作流程ID
     */
    @ApiModelProperty(value = "工作流程ID")
    private Long processId;

    /**
     * 扩展ID
     */
    @ApiModelProperty(value = "扩展ID")
    private Integer extId;

    /**
     * 节点名称
     */
    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    /**
     * 节点处理人/岗位ID/...
     */
    @ApiModelProperty(value = "节点处理人/岗位ID/...")
    private Long deadlineBy;

    /**
     * 节点处理岗位ID
     */
    @ApiModelProperty(value = "节点处理岗位ID")
    private Long deadlinePost;

    /**
     * 节点类型
     */
    @ApiModelProperty(value = "节点类型")
    private Integer type;

    /**
     * 状态Code
     */
    @ApiModelProperty(value = "状态Code")
    private Integer statusCode;

    /**
     * 状态内容
     */
    @ApiModelProperty(value = "状态内容")
    private String statusText;

    /**
     * hook队列名称
     */
    @ApiModelProperty(value = "hook队列名称")
    private String hookName;

    /**
     * 审批人id(多个人审批人用','拼接)
     */
    @ApiModelProperty(value = "审批人id(多个人审批人用','拼接)")
    private String deadlineByUserIds;

    /**
     * 节点处理人名称
     */
    @ApiModelProperty(value = "节点处理人名称")
    private String deadlineName;

    /**
     * 节点处理岗位名称
     */
    @ApiModelProperty(value = "节点处理岗位名称")
    private String deadlinePostName;

    /**
     * 是否是审批人配置(0：审批人配置，1：抄送人配置)
     */
    @ApiModelProperty(value = "是否是审批人配置(0：审批人配置，1：抄送人配置)")
    private Integer isApproverConfig;
}
