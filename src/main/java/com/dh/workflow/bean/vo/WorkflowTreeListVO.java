package com.dh.workflow.bean.vo;

import com.dh.workflow.entity.WorkflowExtinfo;
import com.dh.workflow.entity.WorkflowProcessEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * 工作流树列表返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("工作流树列表返回对象")
public class WorkflowTreeListVO extends WorkflowProcessEntity {

    /**
     * 树节点title
     */
    @ApiModelProperty(value = "树节点title")
    private String title;

    /**
     * 是否展开树节点
     */
    @ApiModelProperty(value = "是否展开树节点")
    private Boolean expand;

    /**
     * 扩展信息列表
     */
    @ApiModelProperty(value = "扩展信息列表")
    List<WorkflowExtinfo> children;
}
