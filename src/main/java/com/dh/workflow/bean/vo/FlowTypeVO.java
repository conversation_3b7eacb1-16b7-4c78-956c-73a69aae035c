package com.dh.workflow.bean.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 工作流程节点类型枚举返回对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiOperation("工作流程节点类型枚举返回对象")
public class FlowTypeVO {
    /**
     * 节点code
     */
    @ApiModelProperty(value = "节点code")
    private Integer code;

    /**
     * 节点名称
     */
    @ApiModelProperty(value = "节点名称")
    private String name;
}
