package com.dh.workflow.controller;


import com.dh.common.util.R;
import com.dh.workflow.entity.FileAnnex;
import com.dh.workflow.service.IFileAnnexService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2020-04-29
 */
@Api(description = "附件管理")
@RestController
@RequestMapping("/fileAnnex")
@AllArgsConstructor
public class FileAnnexController {

    private final IFileAnnexService fileAnnexService;

    @ApiOperation("查询附件")
    @PostMapping(value = "/select")
    public R<List<FileAnnex>> selectListFileAnnex(@RequestBody FileAnnex fileAnnex) {
        return R.ok(fileAnnexService.selectFileAnnex(fileAnnex));
    }

    @ApiOperation("删除附件")
    @PostMapping(value = "/remove/{id}")
    public R<Object> removeFileAnnex(@PathVariable("id") Long id) {
        fileAnnexService.removeById(id);
        return R.ok("删除成功", null);
    }
}
