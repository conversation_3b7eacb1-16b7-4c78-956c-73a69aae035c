package com.dh.workflow.controller;

import com.dh.common.util.R;
import com.dh.workflow.dto.FlowableProcessDefinitionDTO;
import com.dh.workflow.entity.FlowableProcessCategory;
import com.dh.workflow.service.IFlowableProcessCategoryService;
import com.dh.workflow.service.IFlowableProcessDefinitionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/19
 */
@Api(description = "流程分类接口")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/processCategory")
public class FlowableProcessCategoryController {

    private IFlowableProcessCategoryService flowableProcessCategoryService;

    private IFlowableProcessDefinitionService flowableProcessDefinitionService;

    @ApiOperation("保存或修改流程分类")
    @PostMapping("/saveOrUpdate")
    public R saveOrUpdate(@RequestBody FlowableProcessCategory category){
        return R.ok(flowableProcessCategoryService.saveOrUpdate(category));
    }

    @ApiOperation("删除流程分类")
    @PostMapping("/deleteById/{id}")
    public R deleteById(@PathVariable String id){
        List<FlowableProcessDefinitionDTO> list = flowableProcessDefinitionService.findProcessDefByCategoryId(id);
        if(!CollectionUtils.isEmpty(list)){
            return R.failed("流程分类存在流程实例，不能删除!");
        }
        return R.ok(flowableProcessCategoryService.removeById(id));
    }

    @ApiOperation("查询流程分类")
    @PostMapping("/findAll")
    public R findAll(){
        return R.ok(flowableProcessCategoryService.list());
    }
}
