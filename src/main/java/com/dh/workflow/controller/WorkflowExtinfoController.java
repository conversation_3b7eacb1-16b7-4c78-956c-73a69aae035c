package com.dh.workflow.controller;

import com.dh.common.util.R;
import com.dh.workflow.bean.fm.WorkflowExtinfoFM;
import com.dh.workflow.bean.vo.WorkflowExtinfoVO;
import com.dh.workflow.service.IWorkflowExtinfoService;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 工作流程扩展信息控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/22
 */
@RestController
@RequestMapping("/workflowExtinfo")
@ApiOperation("工作流程扩展信息控制器")
public class WorkflowExtinfoController {

    @Resource
    private IWorkflowExtinfoService workflowExtinfoService;

    @GetMapping("/getExtinfoList")
    @ApiOperation("根据id查询工作流扩展信息列表")
    public R<List<WorkflowExtinfoVO>> getExtinfoList(Long id) {
        return R.ok(workflowExtinfoService.getExtinfoList(id));
    }

    @GetMapping("/removeWorkflowExtinfo")
    @ApiModelProperty("删除工作流扩展信息")
    public R<String> removeWorkflowExtinfo(Long id) {
        String result = workflowExtinfoService.removeWorkflowExtinfo(id);
        if (!StringUtils.isEmpty(result)) {
            return R.failed(result);
        }
        return R.ok();
    }

    @PostMapping("/saveOrUpdateExtinfo")
    @ApiOperation("保存或更新工作流扩展信息")
    public R<Boolean> saveOrUpdateExtinfo(@RequestBody WorkflowExtinfoFM workflowExtinfoFM) {
        String result = workflowExtinfoService.saveOrUpdateExtinfo(workflowExtinfoFM);
        if (!StringUtils.isEmpty(result)) {
            return R.failed(result);
        }
        return R.ok();
    }

}
