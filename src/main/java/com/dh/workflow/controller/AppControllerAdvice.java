//package com.dh.workflow.controller;
//
//import com.dh.common.constant.DateConstant;
//import com.dh.common.exception.ValidationException;
//import com.dh.common.util.R;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.validation.ObjectError;
//import org.springframework.web.bind.MethodArgumentNotValidException;
//import org.springframework.web.bind.WebDataBinder;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.InitBinder;
//import org.springframework.web.bind.annotation.RestControllerAdvice;
//
//import java.beans.PropertyEditorSupport;
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.time.LocalTime;
//import java.time.format.DateTimeFormatter;
//import java.util.List;
//
///**
// * 控制增强器，全局处理
// * <AUTHOR>
// * @since 2019/11/07
// */
//@Slf4j
//@RestControllerAdvice
//public class AppControllerAdvice {
//    /**
//     * 全局异常处理
//     * @param ex 系统异常
//     * @return
//     */
//    @ExceptionHandler(Exception.class)
//    public R systemExceptionHandler(Exception ex) {
//        //记录系统异常日志
//        log.error(String.format("Code:%s, Message:%s", 500,500), ex);
//        return R.failed("500", ex.getMessage(), null);
//    }
//
//    /**
//     * 验证信息
//     * @param ex
//     * @return
//     */
//    @ExceptionHandler(ValidationException.class)
//    public R validationExceptionHandler(ValidationException ex) {
//        return R.failed(ex.getMessage());
//    }
//
//    /**
//     * 参数验证错误
//     * @param ex
//     * @return
//     */
//    @ExceptionHandler(value = MethodArgumentNotValidException.class)
//    public R methodArgumentNotValid(MethodArgumentNotValidException  ex)  {
//        List<ObjectError> errors =ex.getBindingResult().getAllErrors();
//        StringBuffer errorMsg=new StringBuffer();
//        errors.stream().forEach(x -> errorMsg.append(x.getDefaultMessage()).append(";"));
//        log.error("---MethodArgumentNotValidException Handler--- ERROR: {}", errorMsg.toString());
//
//        return R.failed(errorMsg.toString());
//    }
//
//    //时间入参处理
//    //参考: https://www.jianshu.com/p/b52db905f020
//    @InitBinder
//    protected void initBinder(WebDataBinder binder) {
//        binder.registerCustomEditor(LocalDate.class, new PropertyEditorSupport() {
//            @Override
//            public void setAsText(String text) throws IllegalArgumentException {
//                setValue(LocalDate.parse(text, DateTimeFormatter.ofPattern(DateConstant.DEFAULT_DATE_FORMAT)));
//            }
//        });
//        binder.registerCustomEditor(LocalDateTime.class, new PropertyEditorSupport() {
//            @Override
//            public void setAsText(String text) throws IllegalArgumentException {
//                setValue(LocalDateTime.parse(text, DateTimeFormatter.ofPattern(DateConstant.DEFAULT_DATETIME_FORMAT)));
//            }
//        });
//        binder.registerCustomEditor(LocalTime.class, new PropertyEditorSupport() {
//            @Override
//            public void setAsText(String text) throws IllegalArgumentException {
//                setValue(LocalTime.parse(text, DateTimeFormatter.ofPattern(DateConstant.DEFAULT_TIME_FORMAT)));
//            }
//        });
//    }
//
//}
