package com.dh.workflow.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.common.exception.ValidationException;
import com.dh.common.util.R;
import com.dh.workflow.dto.FlowableProcessDefinitionDTO;
import com.dh.workflow.entity.FileAnnex;
import com.dh.workflow.service.IFileAnnexService;
import com.dh.workflow.service.IFlowableProcessDefinitionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


/**
 * <AUTHOR>
 * @since 2020/7/17
 */
@Api(description = "流程定义接口")
@Slf4j
@RestController
@RequestMapping("/processDefinition")
@AllArgsConstructor
public class FlowableProcessDefinitionController {

    private IFlowableProcessDefinitionService processDefinitionService;

    private IFileAnnexService fileAnnexService;

    @ApiOperation("根据条件查询流程定义")
    @PostMapping("/getProcessDefinition/{pageNum}/{pageSize}")
    public R getProcessDefinitionPage(@PathVariable Long pageNum,
                                      @PathVariable Long pageSize,
                                      @RequestParam(value = "name",required = false) String name,
                                      @RequestParam(value = "categoryId",required = false) String categoryId){
        if(pageNum == null || pageSize == null){
            return R.failed("分页参数不能为空!");
        }
        Page<FlowableProcessDefinitionDTO> page = new Page<>(pageNum,pageSize);
        IPage<FlowableProcessDefinitionDTO> pageRecord = processDefinitionService.getProcessDefinitionPage(page,name,categoryId);
        for(FlowableProcessDefinitionDTO flowableProcessDefinition : pageRecord.getRecords()){
            flowableProcessDefinition.setFileAnnexList(fileAnnexService.selectByTableId(flowableProcessDefinition.getDeploymentId()));
        }
        return R.ok(pageRecord);
    }

    @ApiOperation("导入流程定义")
    @PostMapping("/doImport/{categoryId}")
    public R doImport(@PathVariable String categoryId, @RequestParam MultipartFile multipartFile,
                      @RequestParam String fileName,@RequestParam String oldFileName,
                      @RequestParam String fileType,@RequestParam String ossFolder){
        if(multipartFile == null){
            throw new ValidationException("请导入流程定义文件!");
        }
        FileAnnex fileAnnex = new FileAnnex();
        fileAnnex.setFileName(fileName);
        fileAnnex.setOldFileName(oldFileName);
        fileAnnex.setFileType(fileType);
        fileAnnex.setOssFolder(ossFolder);
        return processDefinitionService.doImport(categoryId,multipartFile,fileAnnex);
    }

    @ApiOperation("删除流程定义")
    @PostMapping("/delete/{processDefinitionId}")
    public R delete(@PathVariable String processDefinitionId){
        if(StringUtils.isEmpty(processDefinitionId)){
            return R.failed("流程定义id不能为空!");
        }
        return processDefinitionService.delete(processDefinitionId,true);
    }

    @ApiOperation("激活流程定义")
    @PostMapping("/activate/{processDefinitionId}")
    public R activate(@PathVariable String processDefinitionId){
        if(StringUtils.isEmpty(processDefinitionId)){
            return R.failed("流程定义id不能为空!");
        }
        return processDefinitionService.activate(processDefinitionId);
    }

    @ApiOperation("挂起流程定义")
    @PostMapping("/suspend/{processDefinitionId}")
    public R suspend(@PathVariable String processDefinitionId){
        if(StringUtils.isEmpty(processDefinitionId)){
            return R.failed("流程定义id不能为空!");
        }
        return processDefinitionService.suspend(processDefinitionId);
    }

}
