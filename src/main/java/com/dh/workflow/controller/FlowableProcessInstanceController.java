package com.dh.workflow.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.common.util.R;
import com.dh.workflow.dto.FlowableProcessInstanceDTO;
import com.dh.workflow.dto.FlowableProcessInstanceQueryDTO;
import com.dh.workflow.dto.StartProcessInstanceDTO;
import com.dh.workflow.service.IFlowableProcessInstanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/7/7
 */
@Api(description = "流程实例接口")
@Slf4j
@RestController
@RequestMapping("/processInstance")
@AllArgsConstructor
public class FlowableProcessInstanceController {

    private IFlowableProcessInstanceService processInstanceService;

    @ApiOperation("启动流程")
    @PostMapping("/startProcess")
    public R<String> startProcess(@RequestBody @Valid StartProcessInstanceDTO requestDto){
        return processInstanceService.start(requestDto);
    }

    @ApiOperation("获取流程实例中的变量")
    @PostMapping("/findProcessInstanceVariable/{processInstanceId}")
    public R findProcessInstanceVariable(@PathVariable String processInstanceId){
        Map<String,Object> map = null;
        try{
            map = processInstanceService.findProcessInstanceVariable(processInstanceId);
        }catch (Exception e){
            log.error("获取流程实例中的变量反序列化Java对象报错",e);
        }
        return R.ok(map);
    }

    @ApiOperation("查询流程实例列表")
    @PostMapping("/findProcessInstance/{pageNum}/{pageSize}")
    public R findProcessInstance(@RequestBody FlowableProcessInstanceQueryDTO queryDto, @PathVariable Long pageNum,
                                 @PathVariable Long pageSize){
        Page<FlowableProcessInstanceDTO> page = new Page<>(pageNum,pageSize);
        return R.ok(processInstanceService.findProcessInstance(page,queryDto));
    }


    @ApiOperation("test生成流程图")
    @PostMapping("/testImage/{processInstanceId}")
    public R createImage(@PathVariable String processInstanceId){
        return R.ok( processInstanceService.createImage(processInstanceId));
    }

    @ApiOperation("生成流程图片")
    @GetMapping("/createImage/{processInstanceId}")
    public void createImage(@PathVariable String processInstanceId, HttpServletResponse response){
        try {
            byte[] b = processInstanceService.createImage(processInstanceId);
            response.setHeader("Content-type", "text/xml;charset=UTF-8");
            response.getOutputStream().write(b);
        } catch (Exception e) {
            log.error("生成流程图片报错{}" , e);
        }
    }

    @ApiOperation("删除流程实例")
    @PostMapping("/deleteProcessInstanceById/{processInstanceId}")
    public R deleteProcessInstanceById(@PathVariable String processInstanceId){
        return processInstanceService.deleteProcessInstanceById(processInstanceId);
    }

    @ApiOperation("作废流程实例")
    @PostMapping("/stopProcessInstanceById/{processInstanceId}/{taskId}")
    public R stopProcessInstanceById(@PathVariable String processInstanceId,@PathVariable String taskId,
                                     @RequestParam(value="message",required = false) String message){
        return processInstanceService.stopProcessInstanceById(processInstanceId,taskId,message);
    }

}
