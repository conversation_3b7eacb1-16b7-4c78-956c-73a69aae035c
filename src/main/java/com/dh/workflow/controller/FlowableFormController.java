package com.dh.workflow.controller;

import com.dh.common.util.R;
import com.dh.workflow.service.IFlowableFormService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.form.api.FormModel;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2020/7/9
 */
@Api(description = "流程表单接口")
@Slf4j
@RestController
@RequestMapping("/flowableForm")
@AllArgsConstructor
public class FlowableFormController {

    private IFlowableFormService flowableFormService;

    @ApiOperation("查询流程开始表单")
    @PostMapping("/getStartForm/{processDefinitionKey}/{processInstanceId}")
    public R getStartForm(@PathVariable String processDefinitionKey,@PathVariable String processInstanceId){
        if(StringUtils.isEmpty(processDefinitionKey) || StringUtils.isEmpty(processInstanceId)){
            return R.ok("流程定义key和流程实例id不能为空!");
        }
        R<FormModel> data = null;
        try{
            data = flowableFormService.getForm(processDefinitionKey,processInstanceId);
        }catch(Exception e){
            log.error("流程开始表单反序列化Java对象报错",e);
        }
        return data;
    }

    @ApiOperation("查询流程过程中表单")
    @PostMapping("/getTaskForm/{taskId}")
    public R getTaskForm(@PathVariable String taskId){
        if(StringUtils.isEmpty(taskId)){
            return R.failed("任务id不能为空!");
        }
        return flowableFormService.getTaskForm(taskId);
    }


}
