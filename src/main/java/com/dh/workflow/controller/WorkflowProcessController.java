package com.dh.workflow.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.common.util.R;
import com.dh.workflow.bean.fm.GetWorkflowProcessFM;
import com.dh.workflow.bean.fm.WorkflowProcessFM;
import com.dh.workflow.bean.vo.WorkflowTreeListVO;
import com.dh.workflow.entity.WorkflowProcessEntity;
import com.dh.workflow.service.IWorkflowService;
import com.dh.workflow.bean.vo.WorkflowProcessVO;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 工作流程控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@RestController
@RequestMapping("/workflowProcess")
@ApiOperation("工作流程控制器")
public class WorkflowProcessController {

    @Resource
    private IWorkflowService workflowService;

    @PostMapping("/getWorkflowProcessList")
    @ApiOperation("查询工作流程列表")
    public R<IPage<WorkflowProcessVO>> getWorkFlowProcessList(@RequestBody GetWorkflowProcessFM getWorkflowProcessFM) {
        Page<WorkflowProcessVO> page = new Page<>(getWorkflowProcessFM.getPageNum(), getWorkflowProcessFM.getPageSize());
        return R.ok(workflowService.getWorkFlowProcessList(page, getWorkflowProcessFM));
    }

    @PostMapping("/saveOrUpdateWorkflowProcess")
    @ApiOperation("保存或修改工作流程")
    public R<Boolean> saveOrUpdateWorkflowProcess(@RequestBody WorkflowProcessFM workflowProcessFM) {
        String result = workflowService.saveOrUpdateWorkflowProcess(workflowProcessFM);
        if (!StringUtils.isEmpty(result)) {
            return R.failed(result);
        }
        return R.ok();
    }

    @GetMapping("/removeWorkflowProcess")
    @ApiOperation("删除工作流程")
    public R<Boolean> removeWorkflowProcess(Long id) {
        String result = workflowService.removeWorkflowProcess(id);
        if (!StringUtils.isEmpty(result)) {
            return R.failed(result);
        }
        return R.ok();
    }

    @GetMapping("/getWorkflowList")
    @ApiOperation("查询工作流程下拉列表列表")
    public R<List<WorkflowProcessEntity>> getWorkflowList() {
        return R.ok(workflowService.getWorkflowList());
    }

    @GetMapping("/getTreeList")
    @ApiModelProperty("获取工作流树列表")
    public R<List<WorkflowTreeListVO>> getTreeList() {
        return R.ok(workflowService.getTreeList());
    }

}
