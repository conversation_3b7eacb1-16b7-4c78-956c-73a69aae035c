package com.dh.workflow.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.common.util.R;
import com.dh.workflow.dto.BaseProcessDTO;
import com.dh.workflow.dto.TaskDTO;
import com.dh.workflow.service.IFlowableTaskService;
import com.dh.workflow.util.SecurityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.task.api.Task;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2020/7/7
 */
@Api(description = "任务实例接口")
@Slf4j
@RestController
@RequestMapping("/task")
@AllArgsConstructor
public class FlowableTaskController {

    private IFlowableTaskService flowableTaskService;

    @ApiOperation("根据任务id查询任务对象")
    @PostMapping("/findTaskById/{taskId}")
    public R findTaskById(@PathVariable String taskId){
        Task task = flowableTaskService.findTaskById(taskId);
        return R.ok(task);
    }

    @ApiOperation("根据流程实例id获取当前正在执行的任务id")
    @PostMapping("/findTaskIdByProcessInstanceId/{processInstanceId}")
    public R<String> findTaskIdByProcessInstanceId(@PathVariable String processInstanceId){
        TaskDTO task = flowableTaskService.findTaskByProcessInstanceId(processInstanceId);
        if(task == null){
            return R.ok();
        }
        return R.ok(task.getTaskId());
    }

    @ApiOperation("执行任务")
    @PostMapping("/complete")
    public R complete(@RequestBody BaseProcessDTO processDto){
        return flowableTaskService.complete(processDto);
    }

    @ApiOperation("查询当前登陆人待办列表")
    @PostMapping("/toDoTask/{pageNum}/{pageSize}")
    public R toDoTask(@PathVariable Long pageNum,@PathVariable Long pageSize,
                      @RequestParam(value="categoryId",required = false) String categoryId,
                      @RequestParam(value="processDefinitionName",required = false) String processDefinitionName){
        if(pageNum == null || pageSize == null){
            return R.failed("分页参数不能为空!");
        }
        Page<TaskDTO> page = new Page<>(pageNum,pageSize);
        return R.ok(flowableTaskService.getToDoTask(page,SecurityUtil.getUserId().toString(),categoryId,processDefinitionName));
    }

    @ApiOperation("查询当前登陆人已办列表")
    @PostMapping("/getToDealTask/{pageNum}/{pageSize}")
    public R getToDealTask(@PathVariable Long pageNum,@PathVariable Long pageSize,
                           @RequestParam(value="categoryId",required = false) String categoryId,
                           @RequestParam(value="processDefinitionName",required = false) String processDefinitionName){
        Page<TaskDTO> page = new Page<>(pageNum,pageSize);
        return R.ok(flowableTaskService.getToDealTask(page,SecurityUtil.getUserId().toString(),categoryId,processDefinitionName));
    }

    @ApiOperation("查询我发起的流程")
    @PostMapping("/getMyInitProcess/{pageNum}/{pageSize}/{type}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "0:处理中，1：已处理", required = true, paramType = "path", dataType = "String"),
    })
    public R getMyInitProcess(@PathVariable Long pageNum,@PathVariable Long pageSize,@PathVariable Integer type,
                                 @RequestParam(value="categoryId",required = false) String categoryId,
                                 @RequestParam(value="processDefinitionName",required = false) String processDefinitionName){
        Page<TaskDTO> page = new Page<>(pageNum,pageSize);
        return R.ok(flowableTaskService.getMyInitProcess(page,SecurityUtil.getUserId().toString(),categoryId,processDefinitionName,type));
    }

    @ApiOperation("转办任务")
    @PostMapping("/assignTask/{taskId}/{assignee}")
    public R assignTask(@PathVariable String taskId,@PathVariable String assignee,
                        @RequestParam(value = "message",required = false) String message){
        return flowableTaskService.assignTask(taskId,assignee,message);
    }

    @ApiOperation("催办任务")
    @PostMapping("/reminderTask/{taskId}")
    public R reminderTask(@PathVariable String taskId,@RequestParam(value="message",required = false) String message){
        return flowableTaskService.reminderTask(taskId,message);
    }

    @ApiOperation("根据流程实例已办记录")
    @PostMapping("/getTaskByProcessInstanceId/{processInstanceId}")
    public R getTaskByProcessInstanceId( @PathVariable String processInstanceId){
        return R.ok(flowableTaskService.getTaskByProcessInstanceId(processInstanceId));
    }

    @ApiOperation("获取流程监控数据列表")
    @PostMapping("/getProcessMonitor/{pageNum}/{pageSize}/{type}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "0:处理中，1：已处理", required = true, paramType = "path", dataType = "String"),
    })
    public R getProcessMonitor(@PathVariable Long pageNum,@PathVariable Long pageSize,@PathVariable Integer type,
                               @RequestParam(value="categoryId",required = false) String categoryId,
                               @RequestParam(value="processDefinitionName",required = false) String processDefinitionName){
        if(pageNum == null || pageSize == null){
            return R.failed("分页参数不能为空!");
        }
        Page<TaskDTO> page = new Page<>(pageNum,pageSize);
        return R.ok(flowableTaskService.getProcessMonitor(page,categoryId,processDefinitionName,type));
    }

}
