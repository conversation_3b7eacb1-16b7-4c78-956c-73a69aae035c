package com.dh.workflow.controller;

import com.dh.common.entity.MessageInfoDTO;
import com.dh.common.util.R;
import com.dh.dto.bean.dto.workflow.*;
import com.dh.framework.security.exception.BusinessException;
import com.dh.workflow.service.IWorkflowService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 简易线程工作流控制器
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Slf4j
@RestController
@RequestMapping("/simpleFlow")
public class SimpleFlowController {
    @Resource
    IWorkflowService workflowService;

    @ApiOperation("根据任务id查询任务对象")
    @GetMapping("/getTask")
    public R<WorkflowInfoDTO> getTask(@RequestBody FlowTaskDTO flowTaskDTO) {
        WorkflowInfoDTO workflowInfoDTO = workflowService.getTaskInfo(flowTaskDTO, false);
        if (CollectionUtils.isEmpty(workflowInfoDTO.getNodeList()) || CollectionUtils.isEmpty(workflowInfoDTO.getProcedureList()) || workflowInfoDTO.getInstance() == null) {
            return R.failed("任务不存在");
        }
        return R.ok(workflowInfoDTO);
    }

    @ApiOperation("根据任务id查询任务对象(带历史记录)")
    @PostMapping("/getTaskHistory")
    public R<WorkflowInfoDTO> getTaskHistory(@RequestBody FlowTaskDTO flowTaskDTO) {
        WorkflowInfoDTO workflowInfoDTO = workflowService.getTaskInfo(flowTaskDTO, true);
        if (CollectionUtils.isEmpty(workflowInfoDTO.getNodeList()) || CollectionUtils.isEmpty(workflowInfoDTO.getProcedureList()) || workflowInfoDTO.getInstance() == null) {
            return R.failed("任务不存在");
        }
        return R.ok(workflowInfoDTO);
    }

    @ApiOperation("创建任务流程")
    @PostMapping("/createTask")
    public R<CreateRespDTO> createTask(@RequestBody FlowTaskDTO flowTaskDTO) {
        try {
            CreateRespDTO node = workflowService.createTask(flowTaskDTO);

            return R.ok(node);
        } catch (BusinessException exception) {
            log.error("创建任务流程失败: {}", exception.getMessage());
            return R.failed(exception.getMessage());
        }
    }

    @ApiOperation("流程审批")
    @PostMapping("/approvalTask")
    public R<ApprovalRespDTO> approvalTask(@RequestBody ApproveReqDTO flowTaskDTO) {
        ApprovalRespDTO approveRespDTO = workflowService.approvalTask(flowTaskDTO);

        return R.ok(approveRespDTO);
    }

    @ApiOperation("同意流程")
    @PostMapping("/approveTask")
    @Deprecated
    public R<ApprovalRespDTO> approveTask(@RequestBody ApproveTaskDTO approveTaskDTO) {
        ApprovalRespDTO approveRespDTO = workflowService.approveTask(approveTaskDTO);

        return R.ok(approveRespDTO);
    }

    @ApiOperation("拒绝流程")
    @PostMapping("/refuseTask")
    @Deprecated
    public R<ApprovalRespDTO> refuseTask(@RequestBody RefuseTaskDTO refuseTaskDTO) {
        ApprovalRespDTO respDTO = workflowService.refuseTask(refuseTaskDTO);

        return R.ok(respDTO);
    }

    @ApiOperation("撤回流程")
    @PostMapping("/revokeTask")
    @Deprecated
    public R<ApprovalRespDTO> revokeTask(@RequestBody RevokeTaskDTO revokeTaskDTO) {
        ApprovalRespDTO respDTO = workflowService.revokeTask(revokeTaskDTO);

        return R.ok(respDTO);
    }

    @ApiOperation("移交流程")
    @PostMapping("/transferTask")
    public R<Boolean> transferTask(@RequestBody TransferTaskDTO transferTaskDTO) {
        Boolean status = workflowService.transferTask(transferTaskDTO);

        return R.ok(status);
    }
}
