package com.dh.workflow.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.common.util.R;
import com.dh.workflow.bean.fm.GetWorkflowExtinfoFM;
import com.dh.workflow.bean.fm.WorkflowExtinfoFM;
import com.dh.workflow.bean.fm.WorkflowProcessCCNodeFM;
import com.dh.workflow.bean.fm.WorkflowProcessNodeFM;
import com.dh.workflow.bean.vo.FlowTypeVO;
import com.dh.workflow.bean.vo.WorkflowProcessCCNodeVO;
import com.dh.workflow.bean.vo.WorkflowProcessNodeVO;
import com.dh.workflow.constant.FlowTypeEnum;
import com.dh.workflow.service.IWorkflowProcessNodeService;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 工作流程节点控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/22
 */
@RestController
@RequestMapping("/workflowProcessNode")
@ApiOperation("")
public class WorkflowProcessNodeController {

    @Resource
    private IWorkflowProcessNodeService workflowProcessNodeService;

    @PostMapping("/getNodeListById")
    @ApiOperation("根据id查询工作流程节点列表")
    public R<IPage<WorkflowProcessNodeVO>> getNodeListById(@RequestBody GetWorkflowExtinfoFM workflowExtinfoFM) {
        Page<WorkflowProcessNodeVO> page = new Page<>(workflowExtinfoFM.getPageNum(), workflowExtinfoFM.getPageSize());
        return R.ok(workflowProcessNodeService.getNodeListById(page,workflowExtinfoFM));
    }

    @PostMapping("/saveOrUpdateWorkflowProcessNode")
    @ApiOperation("保存或更新工作流程节点")
    public R<Boolean> saveOrUpdateWorkflowProcessNode(@RequestBody WorkflowProcessNodeFM workflowProcessNodeFM) {
        String result = workflowProcessNodeService.saveOrUpdateWorkflowProcessNode(workflowProcessNodeFM);
        if (!StringUtils.isEmpty(result)) {
            return R.failed(result);
        }
        return R.ok();
    }

    @GetMapping("/removeWorkflowProcessNode")
    @ApiOperation("删除工作流程节点")
    public R<Boolean> removeWorkflowProcessNode(Long id) {
        String result = workflowProcessNodeService.removeWorkflowProcessNode(id);
        if (!StringUtils.isEmpty(result)) {
            return R.failed(result);
        }
        return R.ok();
    }

    @GetMapping("/getFlowTypeEnum")
    @ApiOperation("获取工作流程节点类型枚举")
    public R<List<FlowTypeVO>> getFlowTypeEnum() {
        List<FlowTypeVO> list = Arrays.stream(FlowTypeEnum.values())
                .map(flowType -> {
                    FlowTypeVO flowTypeVO = new FlowTypeVO();
                    flowTypeVO.setCode(flowType.getCode());
                    flowTypeVO.setName(flowType.getName());
                    return flowTypeVO;
                })
                .collect(Collectors.toList());
        return R.ok(list);
    }

    @GetMapping("/getCCInfoById")
    @ApiOperation("根据id查询抄送人信息")
    public R<WorkflowProcessCCNodeVO> getCCInfoById(Long processId, Long extId) {
        return R.ok(workflowProcessNodeService.getCCInfoById(processId,extId));
    }

    @PostMapping("/saveOrUpdateWorkflowProcessCCNode")
    @ApiOperation("保存或更新抄送人节点")
    public R<Boolean> saveOrUpdateWorkflowProcessCCNode(@RequestBody WorkflowProcessCCNodeFM workflowProcessNodeFM) {
        String result = workflowProcessNodeService.saveOrUpdateWorkflowProcessCCNode(workflowProcessNodeFM);
        if (!StringUtils.isEmpty(result)) {
            return R.failed(result);
        }
        return R.ok();
    }

    @GetMapping("/removeWorkflowProcessCCNode")
    @ApiOperation("删除抄送人节点")
    public R<Boolean> removeWorkflowProcessCCNode(Long processId, Integer extId) {
        String result = workflowProcessNodeService.removeWorkflowProcessCCNode(processId, extId);
        if (!StringUtils.isEmpty(result)) {
            return R.failed(result);
        }
        return R.ok();
    }

}
