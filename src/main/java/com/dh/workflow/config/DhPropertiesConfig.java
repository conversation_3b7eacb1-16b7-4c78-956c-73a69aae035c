package com.dh.workflow.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 调业务系统url
 * <AUTHOR>
 * @since 2020/7/7
 */
@Configuration
@Data
public class DhPropertiesConfig {

    private String identityUserUrl;

    public String getIdentityUserUrl() {
        identityUserUrl = "/Auth/sys/flowableIdentity/findByUserId";
        if (url == null) {
            return identityUserUrl;
        }
        return url + identityUserUrl;
    }

    public String getIdentityGroupUrl() {
        identityGroupUrl = "/Auth/sys/flowableIdentity/findByGroupId";
        if (url == null) {
            return identityGroupUrl;
        }
        return url + identityGroupUrl;
    }

    public String getIdentityUserListUrl() {
        identityUserListUrl = "/Auth/sys/flowableIdentity/findUserByCondition";
        if (url == null) {
            return identityUserListUrl;
        }
        return url + identityUserListUrl;
    }

    public String getIdentityGroupListUrl() {
        identityGroupListUrl = "/Auth/sys/flowableIdentity/findGroupByCondition";
        if (url == null) {
            return identityGroupListUrl;
        }
        return url + identityGroupListUrl;
    }

    private String  identityGroupUrl;

    private String identityUserListUrl;

    private String identityGroupListUrl;

    /**
     * 服务端验证认证信息地址
     **/
    @Value("${dh.dto.server.url}")
    private String url;

}
