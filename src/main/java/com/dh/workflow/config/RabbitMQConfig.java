package com.dh.workflow.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.amqp.support.converter.MessageConverter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import javax.annotation.Resource;

/**
 * RabbitMQConfig
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class RabbitMQConfig {
    public static final String WORKFLOW_HOOK_EXCHANGE = "workflow.hook.exchange";

    @Resource
    private RabbitAdmin rabbitAdmin;

    @Bean
    public DirectExchange directExchange() {
        return new DirectExchange(WORKFLOW_HOOK_EXCHANGE, true, false);
    }

    @Bean
    public MessageConverter jsonMessageConverter(ObjectMapper objectMapper) {
        return new Jackson2JsonMessageConverter(objectMapper);
    }

    /**
     * 动态创建并绑定队列、交换机
     *
     * @param queueName  队列名称
     * @param routingKey 路由键
     */
    public void dynamicBind(String queueName, String routingKey) {
        log.debug("bind queue:{} with routingKey:{}", queueName, routingKey);

        // 声明队列
        Queue queue = new Queue(queueName, true);
        rabbitAdmin.declareQueue(queue);

        // 绑定队列和交换机
        Binding binding = BindingBuilder.bind(queue).to(directExchange()).with(routingKey);
        rabbitAdmin.declareBinding(binding);
    }

}
