package com.dh.workflow.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2020/7/15
 */
@Configuration
@ConfigurationProperties(prefix = "flowable")
@Data
public class FlowableConfig {

    private String activityFontName;

    private String labelFontName;

    private String annotationFontName;
}
