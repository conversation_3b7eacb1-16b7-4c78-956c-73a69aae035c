package com.dh.workflow.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.dh.common.constant.DbConstant;
import com.dh.workflow.util.SecurityUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * mybatis 配置类
 * <AUTHOR>
 * @since 2010/11/06
 */
@EnableTransactionManagement
@Configuration
@MapperScan(basePackages = {"com.dh.workflow.dao"})
public class MybatisPlusConfig {

    /*
     * 分页插件
     */
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        PaginationInterceptor interceptor  =  new PaginationInterceptor();
        //设置最大单页限制数量
        //interceptor.setLimit(AppConstant.GLOBAL_DATA_MAX_LIMIT);
        return interceptor;
    }

    /**
     * mybaits-plus 字段自动填充
     * @return MetaObjectHandler
     */
    @Bean
    public MetaObjectHandler myMetaObjectHandler(){
        return new MetaObjectHandler(){
            @Override
            public void insertFill(MetaObject metaObject) {
                Long createBy = Optional.ofNullable(SecurityUtil.getUserId()).orElse(1L);
                setInsertFieldValByName(DbConstant.FIELD_CREATE_BY.getValue(), createBy, metaObject);
                setInsertFieldValByName(DbConstant.FIELD_CREATE_DATE.getValue(), LocalDateTime.now(), metaObject);
                setInsertFieldValByName(DbConstant.FIELD_UPDATE_BY.getValue(), createBy, metaObject);
                setInsertFieldValByName(DbConstant.FIELD_UPDATE_DATE.getValue(), LocalDateTime.now(), metaObject);
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                Long updateBy = Optional.ofNullable(SecurityUtil.getUserId()).orElse(1L);
                setUpdateFieldValByName(DbConstant.FIELD_UPDATE_BY.getValue(),updateBy, metaObject);
                setUpdateFieldValByName(DbConstant.FIELD_UPDATE_DATE.getValue(),LocalDateTime.now(), metaObject);
            }
        };
    }
}
