package com.dh.workflow.util;

import com.dh.common.constant.LanguageConstant;
import com.dh.common.dto.SysUserDTO;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

public class SecurityUtil {

    /**
     * 当前登录用户
     * @return
     */
    public static SysUserDTO getUser(){
        if (RequestContextHolder.getRequestAttributes() != null) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            SysUserDTO user = (SysUserDTO) request.getAttribute("userInfo");
            return user;
        } else {
            return null;
        }
    }


    /**
     * 当前登录用户id
     * @return
     */
    public static Long getUserId() {
        if (RequestContextHolder.getRequestAttributes() != null) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            SysUserDTO user = (SysUserDTO) request.getAttribute("userInfo");
            if (user != null) {
                return user.getId();
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 当前登陆用户的岗位
     * @return
     */
    public static Long getUserPostId(){
        if (RequestContextHolder.getRequestAttributes() != null) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            SysUserDTO user = (SysUserDTO) request.getAttribute("userInfo");
            if (user != null) {
                return user.getPostId();
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    public static String getToken(){
        if (RequestContextHolder.getRequestAttributes() != null) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String token = (String) request.getHeader("token");
            return token;
        } else {
            return null;
        }
    }

    /**
     * 当前登陆用户的姓名
     * @return
     */
    public static String getUniqueName() {
        if (RequestContextHolder.getRequestAttributes() != null) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            SysUserDTO user = (SysUserDTO) request.getAttribute("userInfo");
            return user.getRealName();
        } else {
            return null;
        }
    }

    public static String getLanguage(){
        return LocaleContextHolder.getLocale().toLanguageTag() != null ? LocaleContextHolder.getLocale().toLanguageTag() : LanguageConstant.LANGUAGE_CN;
    }
}
