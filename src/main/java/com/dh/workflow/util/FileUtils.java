package com.dh.workflow.util;

import com.dh.workflow.config.FilePropertiesConfig;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2020/5/21
 */
@Slf4j
@Component
@AllArgsConstructor
public class FileUtils {

    private FilePropertiesConfig filePropertiesConfig;

    public void sendDeleteFile(String  ossFolder,String fileName) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpGet httpGet = new HttpGet(filePropertiesConfig.getDeleteFile()+"?ossFolder="+ossFolder+"&fileName="+fileName);

        httpGet.setHeader("Content-Type", "application/json; charset=utf-8");
        try {
            CloseableHttpResponse response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String responseStr = EntityUtils.toString(entity, "UTF-8");
                log.info("删除oss文件", responseStr);
            }
        } catch (Exception e) {
            log.error("删除oss文件", e);
        }
    }

}
