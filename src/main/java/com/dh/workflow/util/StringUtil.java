package com.dh.workflow.util;

/**
 * <AUTHOR>
 * @since 2020/7/8
 */
public class StringUtil {

    public static boolean  isNotBlank(String str){
        int strLen;
        if (str != null && (strLen = str.length()) != 0) {
            for(int i = 0; i < strLen; ++i) {
                if (!Character.isWhitespace(str.charAt(i))) {
                    return false;
                }
            }
            return true;
        } else {
            return true;
        }
    }

}
