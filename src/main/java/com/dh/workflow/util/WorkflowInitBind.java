package com.dh.workflow.util;

import com.dh.workflow.config.RabbitMQConfig;
import com.dh.workflow.service.IWorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Set;


/**
 * 工作流全部初始化routing key和queue绑定
 *
 * <AUTHOR>
 * @since 2025/05/30
 */
//@Slf4j
//@Component
//public class WorkflowInitBind {
//    @Resource
//    RabbitMQConfig rabbitMQConfig;
//
//    @Resource
//    IWorkflowService workflowService;
//
//    @PostConstruct
//    public void init() {
//        log.info("开始初始化工作流Hook queue绑定");
//        // 获取所有的钩子列表
//        Set<String> hooks = workflowService.getHooks();
//        hooks.forEach(hook -> {
//            String queueName = hook + ".queue";
//            String routingKey = hook + ".routing";
//
//            log.info("RabbitMQ exchange bind routing [{}] to queue [{}]", routingKey, queueName);
//            rabbitMQConfig.dynamicBind(queueName, routingKey);
//        });
//    }
//}
