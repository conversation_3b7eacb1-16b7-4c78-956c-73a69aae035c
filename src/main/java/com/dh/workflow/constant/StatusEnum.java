package com.dh.workflow.constant;

import com.dh.dto.bean.dto.workflow.enums.ArrayValuable;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 工作流状态枚举
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Getter
public enum StatusEnum implements ArrayValuable<Integer> {
    // 撤回
    REVOKE(-2, "已撤回"),
    // 拒绝
    REFUSE(-1, "已驳回"),
    // 默认
    DEFAULT(0, "初始化"),
    // 已完成
    FINISHED(99, "审批结束");

    private final int code;
    private final String name;

    StatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String toString() {
        return this.name;
    }

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(StatusEnum::getCode).toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
