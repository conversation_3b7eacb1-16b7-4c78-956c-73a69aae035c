package com.dh.workflow.constant;

import com.dh.dto.bean.dto.workflow.enums.ArrayValuable;
import lombok.Getter;

import java.util.Arrays;

/**
 * 工作流节点类型
 *
 * <AUTHOR>
 * @since 2025/05/20
 */
@Getter
public enum FlowTypeEnum implements ArrayValuable<Integer> {
    // code 98,99以及下面包含code已使用，请勿重复设置
    //空节点, 默认跳过的节点))
    None(0, "跳过节点"),
    User(1, "指定用户"),
    PostUser(2, "按职位检索用户"),
    DeptPostUser(3, "按部门岗位"),
    DeptManagerUser(4,"部门分管副总"),
    Publisher(5,"发布人"),
    Superior(6,"申请人上级");
    private final int code;
    private final String name;

    FlowTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String toString() {
        return this.name;
    }

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(FlowTypeEnum::getCode).toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
