package com.dh.workflow.constant;

/**
 * 审批意见的类型
 */
public enum CommentTypeEnum {
    SP("审批"),
    BH("驳回"),
    QS("签收"),
    ZB("转办"),
    TJ("提交"),
    SPJS("审批结束"),
    ZF("作废"),
    LCZZ("流程终止");
    private String name;//名称

    /**
     * 通过type获取Msg
     *
     * @param type
     * @return
     * @Description:
     */
    public static String getEnumMsgByType(String type) {
        for (CommentTypeEnum e : CommentTypeEnum.values()) {
            if (e.toString().equals(type)) {
                return e.name;
            }
        }
        return "";
    }
    private CommentTypeEnum(String name) {
        this.name = name;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
}
