package com.dh.workflow.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dh.workflow.entity.MessageInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 消息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-11
 */
@Repository
public interface MessageInfoMapper extends BaseMapper<MessageInfo> {
    void deleteByTime(Long createBy, String createDate);
    //根据Id修改状态
    int upMessageType(Long id);
    //查询执行力推送的消息，根据参数集合、推送人名称、接收人ID
    List<MessageInfo> findByIndexes(@Param("value") String value, @Param("userName") String userName, @Param("id") Long id);

    MessageInfo findById(String id);
    //查询是否变成了已办
    int SelMessageId(Long id);

}
