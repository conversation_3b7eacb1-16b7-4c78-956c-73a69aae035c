package com.dh.workflow.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dh.dto.bean.dto.workflow.UserDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.workflow.bean.fm.GetWorkflowExtinfoFM;
import com.dh.workflow.bean.vo.WorkflowProcessCCNodeVO;
import com.dh.workflow.bean.vo.WorkflowProcessNodeVO;
import com.dh.workflow.entity.WorkflowProcessNodeEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025/04/02
 */
public interface WorkflowProcessNodeMapper extends BaseMapper<WorkflowProcessNodeEntity> {
    List<UserDTO> queryPostUsers(Long postId);
    List<UserDTO> queryUsers(String deadlineByUserIds);
    List<UserDTO> queryDeptPostUsers(Long deptId, Long postId);

    /**
     *
     * @Author: 武琦超
     * @Date: 2025/5/21 15:31
     * @param page 分页对象
     * @param workflowExtinfoFM 查询参数
     * @return: null
     **/
    IPage<WorkflowProcessNodeVO> getNodeListById(Page<WorkflowProcessNodeVO> page, @Param("workflowExtinfoFM") GetWorkflowExtinfoFM workflowExtinfoFM);

    /**
     * 通过审批任务创建人id查对应的部门主管
     *
     * @Author: 武琦超
     * @Date: 2025/5/30 9:49
     * @param userId 审批任务创建人id
     * @return: null
     **/
    List<UserDTO> queryDeptManagerUsers(Long userId);

    /**
     * 指定上级查询审批人
     * @Author: 武琦超
     * @Date: 2025/7/16 9:13
     * @param level 指定上级级别
     * @param createdBy 申请人id
     * @return: null
     **/
    List<UserDTO> querySuperiorUsers(@Param("level") Long level, @Param("createBy") Long createdBy);

    /**
     * 根据id查询抄送人信息，每个流程对应的审批人信息仅有一条
     * @Author: 武琦超
     * @Date: 2025/7/16 14:59
     * @param processId 工作流id
     * @param extId 扩展id
     * @return: null
     **/
    WorkflowProcessCCNodeVO getCCInfoById(@Param("processId") Long processId, @Param("extId") Long extId);

    /**
     * 根据申请人查询用户
     * @Author: 武琦超
     * @Date: 2025/7/16 18:09
     * @param createdBy 申请人id
     * @return: null
     **/
    List<UserDTO> queryApproveUserId(Long createdBy);
}
