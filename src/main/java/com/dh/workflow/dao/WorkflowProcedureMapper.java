package com.dh.workflow.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dh.dto.bean.dto.workflow.WorkflowProcedureDTO;
import com.dh.workflow.entity.WorkflowProcedureEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WorkflowProcedureMapper extends BaseMapper<WorkflowProcedureEntity> {
    List<WorkflowProcedureDTO> queryProcedureList(@Param("processInstId") Long processInstId);
    List<WorkflowProcedureDTO> queryProcedureHistory(@Param("instIds") List<Long> instIds);
}
