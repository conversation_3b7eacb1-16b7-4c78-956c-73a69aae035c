package com.dh.workflow.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.workflow.dto.FlowableProcessInstanceDTO;
import com.dh.workflow.dto.FlowableProcessInstanceQueryDTO;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/7/8
 */
public interface FlowableProcessInstanceMapper {

    Page<FlowableProcessInstanceDTO> findProcessInstance(Page<FlowableProcessInstanceDTO> page, @Param("queryDto") FlowableProcessInstanceQueryDTO queryDto);

    List<Map<String,Object>> findHisVariable(@Param("processInstanceId") String processInstanceId);
}
