package com.dh.workflow.dao;

import com.dh.workflow.dto.CommentDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/8
 */
public interface FlowableCommentMapper  {

    /**
     * 通过流程实例id获取审批意见列表
     * @param processInstanceId 流程实例id
     * @return
     */
   List<CommentDTO> getFlowCommentVosByProcessInstanceId(@Param("processInstanceId") String processInstanceId);

}
