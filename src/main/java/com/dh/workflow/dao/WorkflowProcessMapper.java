package com.dh.workflow.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.dto.bean.dto.file.InfraFileDTO;
import com.dh.workflow.bean.fm.GetWorkflowProcessFM;
import com.dh.workflow.bean.vo.WorkflowProcessVO;
import com.dh.workflow.bean.vo.WorkflowTreeListVO;
import com.dh.workflow.entity.WorkflowProcessEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025/04/02
 */
public interface WorkflowProcessMapper extends BaseMapper<WorkflowProcessEntity> {

    /**
     *
     * @Author: 武琦超
     * @Date: 2025/5/20 14:56
     * @param page 分页对象
     * @param getWorkflowProcessFM 查询参数
     * @return: null
     **/
    IPage<WorkflowProcessVO> getWorkFlowProcessList(Page<WorkflowProcessVO> page,@Param("workflowProcessFM") GetWorkflowProcessFM getWorkflowProcessFM);

    /**
     * 获取工作流树列表
     *
     * @Author: 武琦超
     * @Date: 2025/5/22 15:29
     * @return: null
     **/
    List<WorkflowTreeListVO> getTreeList();

    /**
     * 获取表单附件信息
     *
     * @param fileIds
     * @return
     */
    List<InfraFileDTO> getFormItemFiles(@Param("fileIds")List<Long> fileIds);
}
