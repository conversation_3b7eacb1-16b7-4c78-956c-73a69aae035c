package com.dh.workflow.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.workflow.dto.FlowableProcessDefinitionDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/16
 */

public interface FlowableProcessDefinitionMapper {

    IPage<FlowableProcessDefinitionDTO> getProcessDefinitionPage(Page<FlowableProcessDefinitionDTO> page
            , @Param("name") String name,@Param("categoryId") String categoryId);

    FlowableProcessDefinitionDTO getById(@Param("processDefinitionId") String processDefinitionId);

    List<FlowableProcessDefinitionDTO> findProcessDefByCategoryId(@Param("categoryId") String categoryId);
}
