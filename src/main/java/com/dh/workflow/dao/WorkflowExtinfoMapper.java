package com.dh.workflow.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dh.workflow.bean.vo.WorkflowExtinfoVO;
import com.dh.workflow.entity.WorkflowExtinfo;
import lombok.Data;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 工作流程扩展信息Mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
public interface WorkflowExtinfoMapper extends BaseMapper<WorkflowExtinfo> {

    /**
     * 根据工作流id查询扩展表信息列表
     *
     * @Author: 武琦超
     * @Date: 2025/5/22 9:59
     * @param processId 工作流id
     * @return: null
     **/
    List<WorkflowExtinfoVO> getExtinfoList(@Param("processId") Long processId);
}
