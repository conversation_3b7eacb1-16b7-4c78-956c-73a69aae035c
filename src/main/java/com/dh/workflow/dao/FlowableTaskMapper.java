package com.dh.workflow.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.common.dto.SysUserDTO;
import com.dh.workflow.dto.TaskDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/9
 */
public interface FlowableTaskMapper {

    IPage<TaskDTO> getToDoTask(Page<TaskDTO> page, @Param("userId") String userId,
                               @Param("categoryId")String categoryId,
                               @Param("processDefinitionName") String processDefinitionName);

    IPage<TaskDTO> getToDealTask(Page<TaskDTO> page, @Param("userId") String userId,
                                 @Param("categoryId")String categoryId,
                                 @Param("processDefinitionName") String processDefinitionName);

    IPage<TaskDTO> getMyInitProcess(Page<TaskDTO> page,@Param("userId") String userId,
                                       @Param("categoryId")String categoryId,
                                       @Param("processDefinitionName") String processDefinitionName);

    IPage<TaskDTO> getMyInitProcessDeal(Page<TaskDTO> page,@Param("userId") String userId,
                                        @Param("categoryId")String categoryId,
                                        @Param("processDefinitionName") String processDefinitionName);

    List<TaskDTO> getTaskByProcessInstanceId(@Param("processInstanceId") String processInstanceId);

    TaskDTO findTaskByProcessInstanceId(@Param("processInstanceId")String processInstanceId,@Param("userId") String userId);

    List<SysUserDTO> findAssigneeByTaskId(@Param("taskId") String taskId);
}
