package com.dh.workflow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dh.workflow.bean.fm.WorkflowExtinfoFM;
import com.dh.workflow.bean.vo.WorkflowExtinfoVO;
import com.dh.workflow.entity.WorkflowExtinfo;

import java.util.List;

/**
 * <p>
 * 工作流扩展信息服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
public interface IWorkflowExtinfoService extends IService<WorkflowExtinfo> {


    /**
     * 根据工作流程id查询工作流程扩展信息列表
     *
     * @Author: 武琦超
     * @Date: 2025/5/22 9:56
     * @param id 工作流id
     * @return: null
     **/
    List<WorkflowExtinfoVO> getExtinfoList(Long id);

    /**
     * 删除工作流程下的扩展信息
     *
     * @Author: 武琦超
     * @Date: 2025/5/22 14:20
     * @param id 扩展表id
     * @return: null
     **/
    String removeWorkflowExtinfo(Long id);

    /**
     * 保存或修改工作流程下的扩展信息
     *
     * @Author: 武琦超
     * @Date: 2025/6/23 16:03
     * @param workflowExtinfoFM 工作流扩展信息入参
     * @return: null
     **/
    String saveOrUpdateExtinfo(WorkflowExtinfoFM workflowExtinfoFM);
}
