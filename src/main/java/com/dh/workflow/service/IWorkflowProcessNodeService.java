package com.dh.workflow.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dh.workflow.bean.fm.GetWorkflowExtinfoFM;
import com.dh.workflow.bean.fm.WorkflowProcessCCNodeFM;
import com.dh.workflow.bean.fm.WorkflowProcessNodeFM;
import com.dh.workflow.bean.vo.WorkflowExtinfoVO;
import com.dh.workflow.bean.vo.WorkflowProcessCCNodeVO;
import com.dh.workflow.bean.vo.WorkflowProcessNodeVO;
import com.dh.workflow.bean.vo.WorkflowProcessVO;
import com.dh.workflow.entity.WorkflowProcessNodeEntity;

import java.util.List;

/**
 * <p>
 * 工作流程节点服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/21
 */
public interface IWorkflowProcessNodeService extends IService<WorkflowProcessNodeEntity> {

    /**
     * 根据工作流程id查询工作流程节点列表
     *
     * @Author: 武琦超
     * @Date: 2025/5/21 15:29
     * @param page 分页信息
     * @param workflowExtinfoFM 查询条件
     * @return: null
     **/
    IPage<WorkflowProcessNodeVO> getNodeListById(Page<WorkflowProcessNodeVO> page, GetWorkflowExtinfoFM workflowExtinfoFM);

    /**
     * 保存或修改工作流程节点
     *
     * @Author: 武琦超
     * @Date: 2025/5/22 18:31
     * @param workflowProcessNodeFM 修改保存参数
     * @return: null
     **/
    String saveOrUpdateWorkflowProcessNode(WorkflowProcessNodeFM workflowProcessNodeFM);

    /**
     * 删除工作流节点信息
     *
     * @Author: 武琦超
     * @Date: 2025/5/23 10:07
     * @param id 工作流节点id
     * @return: null
     **/
    String removeWorkflowProcessNode(Long id);

    /**
     * 根据id查询抄送人信息，每个流程对应的审批人信息仅有一条
     * @Author: 武琦超
     * @Date: 2025/7/16 14:57
     * @param processId 工作流id
     * @param extId 扩展id
     * @return: null
     **/
    WorkflowProcessCCNodeVO getCCInfoById(Long processId, Long extId);

    /**
     * 删除抄送人信息
     * @param processId 流程id
     * @param extId 扩展id
     * @return
     */
    String removeWorkflowProcessCCNode(Long processId, Integer extId);

    /**
     * 保存或修改抄送人信息
     * @Author: 武琦超
     * @Date: 2025/7/21 11:07
     * @param workflowProcessNodeFM 抄送人修改保存参数
     * @return: null
     **/
    String saveOrUpdateWorkflowProcessCCNode(WorkflowProcessCCNodeFM workflowProcessNodeFM);
}
