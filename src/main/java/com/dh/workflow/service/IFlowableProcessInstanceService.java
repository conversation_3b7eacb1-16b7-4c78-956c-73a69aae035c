package com.dh.workflow.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.common.util.R;
import com.dh.workflow.dto.FlowableProcessInstanceDTO;
import com.dh.workflow.dto.FlowableProcessInstanceQueryDTO;
import com.dh.workflow.dto.StartProcessInstanceDTO;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/7/7
 */
public interface IFlowableProcessInstanceService {

    /**
     * 启动流程
     * @param processInstanceDto 参数
     * @return
     */
    R start(StartProcessInstanceDTO processInstanceDto);

    Map<String,Object> findProcessInstanceVariable(String  processInstanceId)  throws IOException,ClassNotFoundException ;

    /**
     * 设置表单并启动流程
     * @param processInstanceDto
     * @return
     */
    R startByForm(StartProcessInstanceDTO processInstanceDto);

    /**
     *查询流程实例列表
     * @param page
     * @param queryDto
     * @return
     */
    IPage<FlowableProcessInstanceDTO> findProcessInstance(Page<FlowableProcessInstanceDTO> page, FlowableProcessInstanceQueryDTO queryDto);


    /**
     * 查询我发起的流程
     * @param page
     * @param
     * @return
     */
    IPage<FlowableProcessInstanceDTO> findMyProcessInstance(Page<FlowableProcessInstanceDTO> page);

    /**
     * 获取流程图片
     * @param processInstanceId
     * @return
     */
    byte[] createImage(String processInstanceId);

    /**
     * 删除流程实例
     * @param processInstanceId
     * @return
     */
    R deleteProcessInstanceById(String processInstanceId);

    /**
     * 作废流程实例
     * @param processInstanceId
     * @param message
     * @return
     */
    R stopProcessInstanceById(String processInstanceId,String taskId,String message);

}
