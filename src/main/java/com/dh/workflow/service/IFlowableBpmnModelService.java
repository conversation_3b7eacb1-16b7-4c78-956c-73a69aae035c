package com.dh.workflow.service;

import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.EndEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/8
 */
public interface IFlowableBpmnModelService {

    /**
     * 通过流程定义id获取BpmnModel
     *
     * @param processDefId 流程定义id
     * @return
     */
    public BpmnModel getBpmnModelByProcessDefId(String processDefId);

    /**
     * 获取end节点
     * @param processDefId
     * @return
     */
    List<EndEvent> findEndFlowElement(String processDefId);

}
