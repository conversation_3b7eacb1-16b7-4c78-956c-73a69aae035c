package com.dh.workflow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dh.dto.bean.dto.workflow.vo.FileAnnexVo;
import com.dh.workflow.entity.FileAnnex;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-29
 */
public interface IFileAnnexService extends IService<FileAnnex> {

    /**
     * 查询附件
     *
     * @param fileAnnex 附件信息
     * @return 查询结果
     */
    List<FileAnnex> selectFileAnnex(FileAnnex fileAnnex);

    List<FileAnnex> selectByTableId(String tableId);

    /**
     * 根据表id和表类型查询附件
     * @param tableId 表id列表
     * @param tableType 表类型
     * @return 附件列表
     */
    List<FileAnnexVo> getListByTableIdAndType(Long tableId, Integer tableType);
}
