package com.dh.workflow.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.dto.bean.dto.file.InfraFileDTO;
import com.dh.dto.bean.dto.workflow.vo.WorkflowProcessNodeVo;
import com.dh.workflow.bean.fm.GetWorkflowProcessFM;
import com.dh.workflow.bean.fm.WorkflowProcessFM;
import com.dh.workflow.bean.vo.WorkflowProcessVO;
import com.dh.workflow.bean.vo.WorkflowTreeListVO;
import com.dh.dto.bean.dto.workflow.*;
import com.dh.workflow.entity.WorkflowProcessEntity;
import com.dh.workflow.entity.WorkflowProcessInstEntity;
import com.dh.workflow.entity.WorkflowProcessNodeEntity;

import java.util.List;
import java.util.Set;

/**
 * 工作流服务
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
public interface IWorkflowService {
    /**
     * 获得流程节点列表
     *
     * @param id    流程id
     * @param extId 扩展id
     * @return 流程节点列表
     */
    List<WorkflowProcessNodeEntity> getProcessNodes(Long id, Long extId);

    /**
     * 创建任务流程
     *
     * @param flowTaskDTO 流程任务DTO
     * @return 当前流程节点信息
     */
    CreateRespDTO createTask(FlowTaskDTO flowTaskDTO);

    /**
     * 获取任务信息
     *
     * @param flowTaskDTO 流程任务DTO
     */
    WorkflowInfoDTO getTaskInfo(FlowTaskDTO flowTaskDTO, boolean isHistory);

    /**
     * 同意任务
     *
     * @param approveTaskDTO 同意任务DTO
     * @return 审批结果
     */
    ApprovalRespDTO approveTask(ApproveTaskDTO approveTaskDTO);

    /**
     * 拒绝任务
     *
     * @param refuseTaskDTO 拒绝任务DTO
     * @return 拒绝任务响应DTO
     */
    ApprovalRespDTO refuseTask(RefuseTaskDTO refuseTaskDTO);

    /**
     * 查询工作流列表
     *
     * @Author: 武琦超
     * @Date: 2025/5/20 14:53
     * @param page 分页信息
     * @param getWorkflowProcessFM 查询条件
     * @return: null
     **/
    IPage<WorkflowProcessVO> getWorkFlowProcessList(Page<WorkflowProcessVO> page, GetWorkflowProcessFM getWorkflowProcessFM);

    /**
     * 新增或修改工作流
     *
     * @Author: 武琦超
     * @Date: 2025/5/20 15:19
     * @param workflowProcessFM 添加或修改传参
     * @return: null
     **/
    String saveOrUpdateWorkflowProcess(WorkflowProcessFM workflowProcessFM);

    /**
     * 删除工作流
     *
     * @Author: 武琦超
     * @Date: 2025/5/20 15:37
     * @param id 流程id
     * @return: null
     **/
    String removeWorkflowProcess(Long id);

    /**
     * 查询工作流程下拉列表
     *
     * @Author: 武琦超
     * @Date: 2025/5/21 15:02
     * @return: null
     **/
    List<WorkflowProcessEntity> getWorkflowList();

    /**
     * 撤回任务
     *
     * @param revokeTaskDTO 撤回任务DTO
     * @return 撤回任务响应DTO
     */
    ApprovalRespDTO revokeTask(RevokeTaskDTO revokeTaskDTO);


    /**
     * 流程审批
     * @param flowTaskDTO 流程任务DTO
     * @return 审批结果
     */
    ApprovalRespDTO approvalTask(ApproveReqDTO flowTaskDTO);

    /**
     * 转移任务
     *
     * @param transferTaskDTO 转移任务DTO
     * @return 转移任务响应DTO
     */
    boolean transferTask(TransferTaskDTO transferTaskDTO);

    /**
     * 获取工作流树列表
     *
     * @Author: 武琦超
     * @Date: 2025/5/22 15:28
     * @return: null
     **/
    List<WorkflowTreeListVO> getTreeList();

    /**
     * 查询工作流名称
     *
     * @Author: 武琦超
     * @Date: 2025/5/28 13:58
     * @param processId 工作流id
     * @return: null
     **/
    WorkflowProcessEntity getWorkflowName(Long processId);

    /**
     * 获取所有的钩子列表
     *
     * <AUTHOR>
     * @since 2025/05/30
     * @return 所有的钩子列表
     */
    Set<String> getHooks();

    /**
     * 获取流程节点审批人列表
     *
     * @param workflowProcessInstEntity 流程对象
     * @return 节点列表
     */
    List<WorkflowProcessNodeVo> getNodeList(WorkflowProcessInstEntity workflowProcessInstEntity);

    /**
     * 获取审批状态，如果审批已完成，消除待办
     * @Author: 武琦超
     * @Date: 2025/7/17 9:43
     * @param instId 流程实例id
     * @return: null
     **/
    Boolean doTodo(Long instId);

    /**
     * 获取表单附件信息
     *
     * @param fileIds
     * @return
     */
    List<InfraFileDTO> getFormItemFiles(List<Long> fileIds);
}
