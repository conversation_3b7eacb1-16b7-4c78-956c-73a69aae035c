package com.dh.workflow.service;


import com.dh.dto.bean.dto.workflow.HookCompletedDataDTO;
import com.dh.dto.bean.dto.workflow.HookDataDTO;

/**
 * Hook Producer interface
 * 工作流 Hook 消息发送者接口
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface HookProducer {
    /**
     * 发送消息
     *
     * @param data          消息内容
     * @param hookKey    路由键
     */
    void sendMessage(HookDataDTO data, String hookKey);

    /**
     * 发送完成消息
     *
     * @param data          消息内容
     * @param hookKey    路由键
     */
    void sendCompletedMessage(HookCompletedDataDTO data, String hookKey);
}
