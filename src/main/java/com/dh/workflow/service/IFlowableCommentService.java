package com.dh.workflow.service;

import com.dh.workflow.dto.CommentDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/8
 */
public interface IFlowableCommentService {

    /**
     * 添加备注
     * @param comment 参数
     */
    public void addComment(CommentDTO comment) ;

    /**
     * 通过流程实例id获取审批意见列表
     * @param processInstanceId 流程实例id
     * @return
     */
    public List<CommentDTO> getFlowCommentVosByProcessInstanceId(String processInstanceId) ;
}
