package com.dh.workflow.service;

import com.dh.workflow.config.FlowableConfig;
import com.dh.workflow.util.MyDefaultProcessDiagramGenerator;
import org.flowable.bpmn.model.BpmnModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR> bruce.liu
 * @title: : FlowProcessDiagramGenerator
 * @projectName : flowable
 * @description: 生成图片
 * @date : 2019/11/2516:11
 */

@Service
public class FlowProcessDiagramGenerator extends MyDefaultProcessDiagramGenerator {

    private static final String IMAGE_TYPE = "png";

    @Autowired
    private FlowableConfig flowableConfig;
    /**
     * 生成图片流
     *
     * @param bpmnModel             模型
     * @param highLightedActivities 活动节点
     * @param highLightedFlows      高亮线
     * @return
     */
    public InputStream generateDiagram(BpmnModel bpmnModel, List<String> highLightedActivities, List<String> highLightedFlows) {
        return generateDiagram(bpmnModel, IMAGE_TYPE, highLightedActivities,
                highLightedFlows,flowableConfig.getActivityFontName(), flowableConfig.getLabelFontName(),
                flowableConfig.getAnnotationFontName(),
                null, 1.0, true);
    }

    /**
     * 生成图片流
     *
     * @param bpmnModel 模型
     * @return
     */
    public InputStream generateDiagram(BpmnModel bpmnModel) {
        return generateDiagram(bpmnModel, IMAGE_TYPE, flowableConfig.getActivityFontName(),
                flowableConfig.getLabelFontName(), flowableConfig.getAnnotationFontName(),
                null, 1.0, true);
    }
}
