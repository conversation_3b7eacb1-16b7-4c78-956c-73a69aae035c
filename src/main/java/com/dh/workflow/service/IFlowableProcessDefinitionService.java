package com.dh.workflow.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.common.util.R;
import com.dh.workflow.dto.FlowableProcessDefinitionDTO;
import com.dh.workflow.entity.FileAnnex;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2020/7/16
 */
public interface IFlowableProcessDefinitionService {

    /**
     * 通过条件查询流程定义
     * @param page
     * @param name
     * @return
     */
    IPage<FlowableProcessDefinitionDTO> getProcessDefinitionPage(Page<FlowableProcessDefinitionDTO> page, String name,
                                                                    String categoryId);

    /**
     * 查询流程定义
     * @param processDefinitionId
     * @return
     */
    FlowableProcessDefinitionDTO getProcessDefinitionById(String processDefinitionId);


    /**
     * 导入流程定义
     * @param file
     */
    R doImport(String categoryId, MultipartFile file, FileAnnex fileAnnex);

    /**
     * 删除流程定义
     * @param processDefinitionId
     * @param cascade
     */
    R delete(String processDefinitionId,Boolean cascade);

    /**
     * 激活流程定义
     * @param processDefinitionId
     */
    R activate(String processDefinitionId);

    /**
     * 挂起流程定义
     * @param processDefinitionId
     */
    R suspend(String processDefinitionId);

    List<FlowableProcessDefinitionDTO> findProcessDefByCategoryId(String categoryId);

}
