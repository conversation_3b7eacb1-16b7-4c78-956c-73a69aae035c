package com.dh.workflow.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.common.util.R;
import com.dh.workflow.dto.BaseProcessDTO;
import com.dh.workflow.dto.TaskDTO;
import org.flowable.idm.api.User;
import org.flowable.task.api.Task;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/7
 */
public interface IFlowableTaskService {

    /**
     * 通过任务id获取任务对象
     * @param taskId
     * @return
     */
    Task findTaskById(String taskId);

    /**
     * 执行任务
     * @return
     */
    R<String> complete(BaseProcessDTO processDto);

    /**
     * 通过流程实例id获取流程实例的待办任务审批列表
     * @param processInstanceId
     * @return
     */
    List<User> getApprovers(String processInstanceId);

    /**
     * 查看当前登陆人待办任务列表
     * @param userId
     * @return
     */
    IPage<TaskDTO> getToDoTask(Page<TaskDTO> page, String userId,String categoryId,String processDefinitionName);

    /**
     * 查询已处理的任务列表
     * @param page
     * @param userId
     * @return
     */
    IPage<TaskDTO> getToDealTask(Page<TaskDTO> page, String userId,String categoryId,String processDefinitionName);

    /**
     * 查询我发起的流程
     * @param page
     * @param userId
     * @param categoryId
     * @param processDefinitionName
     * @return
     */
    IPage<TaskDTO> getMyInitProcess(Page<TaskDTO> page,String userId,String categoryId,String processDefinitionName,Integer type);

    IPage<TaskDTO> getProcessMonitor(Page<TaskDTO> page,String categoryId,String processDefinitionName,Integer type);

    /**
     * 转办任务
     */
    R assignTask(String taskId,String assignee,String message);

    /**
     * 催办任务
     * @param taskId
     * @param message
     * @return
     */
    R reminderTask(String taskId,String message);

    List<TaskDTO> getTaskByProcessInstanceId( String processInstanceId);

    TaskDTO findTaskByProcessInstanceId(String processInstanceId);

}
