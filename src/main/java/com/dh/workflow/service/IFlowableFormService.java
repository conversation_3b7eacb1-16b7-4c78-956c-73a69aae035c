package com.dh.workflow.service;

import com.dh.common.util.R;
import com.dh.workflow.dto.FlowableFormFieldDTO;
import org.flowable.form.api.FormModel;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/9
 */
public interface IFlowableFormService {

    R<FormModel> getForm(String processDefinitionKey, String processInstanceId) throws IOException,ClassNotFoundException;

    R<List<FlowableFormFieldDTO>> getTaskForm(String taskId);
}
