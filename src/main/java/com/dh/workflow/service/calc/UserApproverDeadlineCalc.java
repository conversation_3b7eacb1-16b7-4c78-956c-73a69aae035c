package com.dh.workflow.service.calc;

import com.dh.dto.bean.dto.workflow.UserDTO;
import com.dh.dto.bean.dto.workflow.WorkflowProcessNodeDTO;
import com.dh.workflow.dao.WorkflowProcessNodeMapper;
import com.dh.workflow.entity.WorkflowProcedureEntity;
import com.dh.workflow.entity.WorkflowProcessInstEntity;
import com.dh.workflow.service.DeadlineCalc;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 申请人 （抄送人计算）
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
@Service("userApproverDeadlineCalc")
public class UserApproverDeadlineCalc implements DeadlineCalc {
    @Resource
    private WorkflowProcessNodeMapper workflowProcessNodeMapper;

    @Override
    public List<UserDTO> calc(WorkflowProcessInstEntity workflowProcessInstEntity, WorkflowProcessNodeDTO workflowProcessNodeDTO, WorkflowProcedureEntity workflowProcedureEntity) {
        return workflowProcessNodeMapper.queryApproveUserId(workflowProcessInstEntity.getCreatedBy());
    }

    @Override
    public Integer getType() {
        return 99;
    }
}
