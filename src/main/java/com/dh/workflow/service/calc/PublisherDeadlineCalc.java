package com.dh.workflow.service.calc;

import com.dh.dto.bean.dto.workflow.UserDTO;
import com.dh.dto.bean.dto.workflow.WorkflowProcessNodeDTO;
import com.dh.framework.security.exception.BusinessException;
import com.dh.workflow.constant.FlowTypeEnum;
import com.dh.workflow.dao.WorkflowProcessNodeMapper;
import com.dh.workflow.entity.WorkflowProcedureEntity;
import com.dh.workflow.entity.WorkflowProcessInstEntity;
import com.dh.workflow.service.DeadlineCalc;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: 秦帅彬
 * @creat: 2025-06-18  15:46
 * 描述:
 */
@Service("publisherDeadlineCalc")
public class PublisherDeadlineCalc implements DeadlineCalc {
    /**
     * 发布人字段
     */
    public static final String PUBLISHER_FIELD = "publisherId";

    @Resource
    private WorkflowProcessNodeMapper workflowProcessNodeMapper;

    @Override
    public List<UserDTO> calc(WorkflowProcessInstEntity workflowProcessInstEntity, WorkflowProcessNodeDTO workflowProcessNodeDTO, WorkflowProcedureEntity workflowProcedureDTO) {
        if (workflowProcessInstEntity.getParams() == null) {
            throw new BusinessException("未找到对应的节点审批人，请确认节点审批人配置是否正确");
        }
        String publisherId = workflowProcessInstEntity.getParams().get(PUBLISHER_FIELD);

        return workflowProcessNodeMapper.queryUsers(publisherId);
    }

    @Override
    public Integer getType() {
        return FlowTypeEnum.Publisher.getCode();
    }
}