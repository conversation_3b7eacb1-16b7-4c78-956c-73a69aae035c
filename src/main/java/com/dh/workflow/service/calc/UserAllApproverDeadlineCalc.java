package com.dh.workflow.service.calc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dh.dto.bean.dto.workflow.FlowTaskDTO;
import com.dh.dto.bean.dto.workflow.UserDTO;
import com.dh.dto.bean.dto.workflow.WorkflowInfoDTO;
import com.dh.dto.bean.dto.workflow.WorkflowProcessNodeDTO;
import com.dh.dto.bean.dto.workflow.vo.WorkflowProcessNodeVo;
import com.dh.framework.security.exception.BusinessException;
import com.dh.workflow.dao.WorkflowProcessNodeMapper;
import com.dh.workflow.entity.WorkflowProcedureEntity;
import com.dh.workflow.entity.WorkflowProcessInstEntity;
import com.dh.workflow.entity.WorkflowProcessNodeEntity;
import com.dh.workflow.service.DeadlineCalc;
import com.dh.workflow.service.DeadlineCalcFactory;
import com.dh.workflow.service.IWorkflowService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 查询所有参与审批的用户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
@Service("userAllApproverDeadlineCalc")
public class UserAllApproverDeadlineCalc implements DeadlineCalc {
    @Resource
    private WorkflowProcessNodeMapper workflowProcessNodeMapper;
    @Resource
    IWorkflowService workflowService;

    @Override
    public List<UserDTO> calc(WorkflowProcessInstEntity workflowProcessInstEntity, WorkflowProcessNodeDTO workflowProcessNodeDTO, WorkflowProcedureEntity workflowProcedureEntity) {
        // 查询所有审批节点信息
        FlowTaskDTO flowTaskDTO = new FlowTaskDTO();
        flowTaskDTO.setInstanceId(workflowProcessInstEntity.getId());
        List<WorkflowProcessNodeVo> nodeList = workflowService.getNodeList(workflowProcessInstEntity);
        String result = "";
        // 处理审批节点审批人id，拼接deadlineBy，用逗号分隔
        if (nodeList != null) {
            result = nodeList.stream()
                    .map(WorkflowProcessNodeVo::getDeadlineBy)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(","));
        }
//        result = result + "," + workflowProcessInstEntity.getCreatedBy();
        return workflowProcessNodeMapper.queryUsers(result);
    }

    @Override
    public Integer getType() {
        return 98;
    }
}
