package com.dh.workflow.service.calc;

import com.dh.dto.bean.dto.workflow.UserDTO;
import com.dh.dto.bean.dto.workflow.WorkflowProcessNodeDTO;
import com.dh.workflow.constant.FlowTypeEnum;
import com.dh.workflow.dao.WorkflowProcessNodeMapper;
import com.dh.workflow.entity.WorkflowProcedureEntity;
import com.dh.workflow.entity.WorkflowProcessInstEntity;
import com.dh.workflow.service.DeadlineCalc;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 按组织架构的部门内职位检索用户
 *
 * <AUTHOR>
 * @since 2025/05/20
 */
@Service("deptPostDeadlineCalc")
public class DeptPostDeadlineCalc implements DeadlineCalc {
    @Resource
    private WorkflowProcessNodeMapper workflowProcessNodeMapper;

    @Override
    public List<UserDTO> calc(WorkflowProcessInstEntity workflowProcessInstEntity, WorkflowProcessNodeDTO workflowProcessNodeDTO, WorkflowProcedureEntity workflowProcedureEntity) {
        return workflowProcessNodeMapper.queryDeptPostUsers(workflowProcessNodeDTO.getDeadlineBy(), workflowProcessNodeDTO.getDeadlinePost());
    }

    @Override
    public Integer getType() {
        return FlowTypeEnum.DeptPostUser.getCode();
    }
}
