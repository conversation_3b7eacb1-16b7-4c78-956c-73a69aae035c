package com.dh.workflow.service.calc;

import com.dh.dto.bean.dto.workflow.UserDTO;
import com.dh.dto.bean.dto.workflow.WorkflowProcessNodeDTO;
import com.dh.workflow.constant.FlowTypeEnum;
import com.dh.workflow.dao.WorkflowProcessNodeMapper;
import com.dh.workflow.entity.WorkflowProcedureEntity;
import com.dh.workflow.entity.WorkflowProcessInstEntity;
import com.dh.workflow.service.DeadlineCalc;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 岗位处理人计算
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Service("postUserDeadlineCalc")
public class PostUserDeadlineCalc implements DeadlineCalc {
    @Resource
    private WorkflowProcessNodeMapper workflowProcessNodeMapper;

    @Override
    public List<UserDTO> calc(WorkflowProcessInstEntity workflowProcessInstEntity, WorkflowProcessNodeDTO workflowProcessNodeDTO, WorkflowProcedureEntity workflowProcedureDTO) {
        return workflowProcessNodeMapper.queryPostUsers(workflowProcessNodeDTO.getDeadlinePost());
    }

    @Override
    public Integer getType() {
        return FlowTypeEnum.PostUser.getCode();
    }
}
