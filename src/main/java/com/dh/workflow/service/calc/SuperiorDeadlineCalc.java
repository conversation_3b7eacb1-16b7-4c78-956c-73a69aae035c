package com.dh.workflow.service.calc;

import com.dh.dto.bean.dto.workflow.UserDTO;
import com.dh.dto.bean.dto.workflow.WorkflowProcessNodeDTO;
import com.dh.workflow.constant.FlowTypeEnum;
import com.dh.workflow.dao.WorkflowProcessNodeMapper;
import com.dh.workflow.entity.WorkflowProcedureEntity;
import com.dh.workflow.entity.WorkflowProcessInstEntity;
import com.dh.workflow.service.DeadlineCalc;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 申请人直属上级，通过递归查询申请人的第N级上级
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/15
 */
@Service("superiorDeadlineCalc")
public class SuperiorDeadlineCalc implements DeadlineCalc {
    @Resource
    private WorkflowProcessNodeMapper workflowProcessNodeMapper;

    @Override
    public List<UserDTO> calc(WorkflowProcessInstEntity workflowProcessInstEntity, WorkflowProcessNodeDTO workflowProcessNodeDTO, WorkflowProcedureEntity workflowProcedureEntity) {
        return workflowProcessNodeMapper.querySuperiorUsers(workflowProcessNodeDTO.getDeadlineBy(),workflowProcessInstEntity.getCreatedBy());
    }

    @Override
    public Integer getType() {
        return FlowTypeEnum.Superior.getCode();
    }
}
