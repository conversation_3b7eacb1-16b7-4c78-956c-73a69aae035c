package com.dh.workflow.service.impl;

import com.dh.common.util.R;
import com.dh.workflow.dao.FlowableProcessInstanceMapper;
import com.dh.workflow.dto.FlowableFormFieldDTO;
import com.dh.workflow.service.BaseProcessService;
import com.dh.workflow.service.IFlowableFormService;
import lombok.AllArgsConstructor;
import org.flowable.engine.form.FormProperty;
import org.flowable.engine.form.TaskFormData;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.form.api.FormInfo;
import org.flowable.form.api.FormModel;
import org.flowable.form.model.FormField;
import org.flowable.form.model.SimpleFormModel;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/7/9
 */
@Service
@AllArgsConstructor
public class FlowableFormServiceImpl extends BaseProcessService implements IFlowableFormService {

    private FlowableProcessInstanceMapper instanceMapper;

    @Override
    public R<FormModel> getForm(String processDefinitionKey, String processInstanceId) throws IOException,ClassNotFoundException {
        ProcessDefinition pd = repositoryService.createProcessDefinitionQuery().processDefinitionKey(processDefinitionKey)
                .latestVersion().singleResult();
        if(pd == null){
            return R.failed("流程不存在！");
        }
        FormInfo formInfo = runtimeService.getStartFormModel(pd.getId(),processInstanceId);
        if(formInfo == null){
            return R.failed("流程没有开始表单！");
        }
        SimpleFormModel formModel = (SimpleFormModel) formInfo.getFormModel();
        List<FormField> fieldList = formModel.getFields();
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        Map<String, Object> variables = new HashMap<>();
        if(processInstance != null){
            variables = runtimeService.getVariables(processInstanceId);
        }else{
            List<Map<String,Object>> list= instanceMapper.findHisVariable(processInstanceId);
            for(Map<String,Object> map : list){
                String name = map.get("name").toString();
                Object text = null;
                if("serializable".equals(map.get("type").toString())){
                    byte[] bytes = (byte[]) map.get("bytes");
                    ObjectInputStream ois = new ObjectInputStream(new ByteArrayInputStream(bytes));
                    if(name.contains("table")){
                        text = (LinkedHashMap) ois.readObject();
                    }else if(name.contains("List")){
                        text = (ArrayList)ois.readObject();
                    }else{
                        text = ois.readObject();
                    }
                }else{
                    text = map.get("text");
                }
                variables.put(name,text);
            }
        }

        if(!CollectionUtils.isEmpty(fieldList)){
            for(FormField field : fieldList){
                if(variables.containsKey(field.getId())){
                    field.setValue(variables.get(field.getId()));
                }
            }
        }
        return R.ok(formInfo.getFormModel());
    }

    @Override
    public R<List<FlowableFormFieldDTO>> getTaskForm(String taskId) {
        TaskFormData taskFormData = formService.getTaskFormData(taskId);
        System.out.println(taskFormData.getFormKey());
        List<FormProperty> propertyList = taskFormData.getFormProperties();
        List<FlowableFormFieldDTO> fieldList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(propertyList)){
            for(FormProperty property : propertyList){
                FlowableFormFieldDTO fieldDTO = new FlowableFormFieldDTO();
                fieldDTO.setId(property.getId());
                fieldDTO.setName(property.getName());
                fieldDTO.setValue(property.getValue());
                fieldDTO.setType(getValue(property.getType().getName()));
                fieldDTO.setRequired(property.isRequired());
                fieldDTO.setReadOnly(property.isReadable());
                fieldList.add(fieldDTO);
            }
        }
       return R.ok(fieldList);
    }

    private String getValue(String type){
        Map<String,String> map = new HashMap();
        map.put("string","dh_text");
        map.put("long","dh_text");
        map.put("double","dh_text");
        map.put("date","dh_date");
        map.put("boolean","dh_text");
        return map.get(type);
    }
}
