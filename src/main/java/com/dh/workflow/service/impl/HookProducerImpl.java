package com.dh.workflow.service.impl;

import com.dh.dto.bean.dto.workflow.HookCompletedDataDTO;
import com.dh.dto.bean.dto.workflow.HookDataDTO;
import com.dh.workflow.config.RabbitMQConfig;
import com.dh.workflow.service.HookProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * Hook生产者实现
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Slf4j
@Service
public class HookProducerImpl implements HookProducer {
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送消息
     *
     * @param data    消息内容
     * @param hookKey 路由键
     */
    @Override
    public void sendMessage(HookDataDTO data, String hookKey) {
        if (StringUtils.isEmpty(hookKey)) {
            return;
        }
        String routingKey = hookKey + ".routing";
        log.info("sendMessage: routingKey={}, data={}", routingKey, data);

        rabbitTemplate.convertAndSend(RabbitMQConfig.WORKFLOW_HOOK_EXCHANGE, routingKey, data);
    }

    /**
     * 发送完成消息
     *
     * @param data    消息内容
     * @param hookKey 路由键
     */
    @Override
    public void sendCompletedMessage(HookCompletedDataDTO data, String hookKey) {
        if (StringUtils.isEmpty(hookKey)) {
            return;
        }
        String routingKey = hookKey + ".routing";
        log.info("sendCompletedMessage: routingKey={}, data={}", routingKey, data);

        rabbitTemplate.convertAndSend(RabbitMQConfig.WORKFLOW_HOOK_EXCHANGE, routingKey, data);
    }
}
