package com.dh.workflow.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.common.constant.MessageChannelTypeConstant;
import com.dh.common.constant.MessageTypeConstant;
import com.dh.common.dto.SysUserDTO;
import com.dh.common.entity.MessageChannelDTO;
import com.dh.common.entity.MessageInfoDTO;
import com.dh.common.util.MessageUtil;
import com.dh.common.util.R;
import com.dh.workflow.constant.CommentTypeEnum;
import com.dh.workflow.dao.FlowableTaskMapper;
import com.dh.workflow.dto.BaseProcessDTO;
import com.dh.workflow.dto.TaskDTO;
import com.dh.workflow.service.BaseProcessService;
import com.dh.workflow.service.IFlowableTaskService;
import com.dh.workflow.util.SecurityUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.idm.api.User;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/7/7
 */
@Slf4j
@Service
@AllArgsConstructor
public class FlowableTaskServiceImpl extends BaseProcessService implements IFlowableTaskService {

    private FlowableTaskMapper flowableTaskMapper;

    private MessageUtil messageUtil;

    @Override
    public Task findTaskById(String taskId) {
        return taskService.createTaskQuery().taskId(taskId).singleResult();
    }

    @Override
    public R complete(BaseProcessDTO processDto) {
        //1.查看当前任务是存在
        String userId = SecurityUtil.getUserId().toString();
        TaskEntity taskEntity = (TaskEntity) taskService.createTaskQuery().taskId(processDto.getTaskId()).singleResult();
        if(taskEntity != null ){
            String taskId = processDto.getTaskId();
            if(DelegationState.PENDING.equals(taskEntity.getDelegationState())){
                //暂时不处理委派任务
            }else{
                taskService.setAssignee(processDto.getTaskId(),userId);
                Map<String,Object> paramMap = processDto.getVariables();
                paramMap.put("processInstanceId",processDto.getProcessInstanceId());
                taskService.complete(processDto.getTaskId(),paramMap);
                String type = StringUtils.isEmpty(processDto.getType()) ? CommentTypeEnum.SP.toString():processDto.getType();
                //生成审批意见
                this.addComment(taskId,userId,processDto.getProcessInstanceId(),type,processDto.getMessage());
            }
            return R.ok();
        }
        return R.failed("没有此任务，请确认!");
    }

    @Override
    public List<User> getApprovers(String processInstanceId) {
        List<User> users = new ArrayList<>();
        List<Task> list = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        if (CollectionUtils.isNotEmpty(list)) {
            for(Task task : list){
                if(StringUtils.isNotEmpty(task.getAssignee())){
                    //1.审批人ASSIGNEE_是用户id
                    User user = identityService.createUserQuery().userId(task.getAssignee()).singleResult();
                    if (user != null) {
                        users.add(user);
                    }
                    //2.审批人ASSIGNEE_是组id
                    List<User> gusers = identityService.createUserQuery().memberOfGroup(task.getAssignee()).list();
                    if (CollectionUtils.isNotEmpty(gusers)) {
                        users.addAll(gusers);
                    }
                }else{
                    List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
                    if (CollectionUtils.isNotEmpty(identityLinks)) {
                        identityLinks.forEach(identityLink -> {
                            //3.审批人ASSIGNEE_为空,用户id
                            if (StringUtils.isNotEmpty(identityLink.getUserId())) {
                                User user = identityService.createUserQuery().orderByUserId().singleResult();
                                if (user != null) {
                                    users.add(user);
                                }
                            } else {
                                //4.审批人ASSIGNEE_为空,组id
                                List<User> gusers = identityService.createUserQuery().memberOfGroup(identityLink.getGroupId()).list();
                                if (CollectionUtils.isNotEmpty(gusers)) {
                                    users.addAll(gusers);
                                }
                            }
                        });
                    }
                }
            }
        }
        return users;
    }

    @Override
    public IPage<TaskDTO> getToDoTask(Page<TaskDTO> page, String userId,String categoryId,String processDefinitionName) {
        return flowableTaskMapper.getToDoTask(page,userId,categoryId,processDefinitionName);
    }

    @Override
    public IPage<TaskDTO> getToDealTask(Page<TaskDTO> page, String userId,String categoryId,String processDefinitionName) {
        return flowableTaskMapper.getToDealTask(page,userId,categoryId,processDefinitionName);
    }

    @Override
    public IPage<TaskDTO> getMyInitProcess(Page<TaskDTO> page, String userId, String categoryId,
                                           String processDefinitionName,Integer type) {
        if(type == 0){
            return flowableTaskMapper.getMyInitProcess(page,userId,categoryId,processDefinitionName);
        }
        return flowableTaskMapper.getMyInitProcessDeal(page,userId,categoryId,processDefinitionName);
    }

    @Override
    public IPage<TaskDTO> getProcessMonitor(Page<TaskDTO> page, String categoryId, String processDefinitionName,
                                            Integer type) {
        if(type == 0){
            return flowableTaskMapper.getMyInitProcess(page,null,categoryId,processDefinitionName);
        }
        return flowableTaskMapper.getMyInitProcessDeal(page,null,categoryId,processDefinitionName);
    }

    @Override
    public R assignTask(String taskId,String assignee,String message) {
        TaskEntityImpl currTask = (TaskEntityImpl) taskService.createTaskQuery().taskId(taskId).singleResult();
        if(currTask != null){
            String userId = SecurityUtil.getUserId().toString();
            //1.生成历史记录
            TaskEntity task = this.createSubTask(currTask,userId);
            //2.添加审批记录
            this.addComment(task.getId(),userId, task.getProcessInstanceId(), CommentTypeEnum.ZB.toString(), message);
            taskService.complete(task.getId());
            //3.转办
            taskService.setAssignee(taskId, assignee);
            taskService.setOwner(taskId,userId);
            return R.ok("转办成功!");
        }
        return R.failed("没有运行时的任务实例！");
    }

    @Override
    public R reminderTask(String taskId, String message) {
        TaskEntityImpl currTask = (TaskEntityImpl) taskService.createTaskQuery().taskId(taskId).singleResult();
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(currTask.getProcessInstanceId()).singleResult();
        if(currTask == null){
            return R.failed("没有运行时的任务实例");
        }
        if(processInstance == null){
            return R.failed("流程实例id不存在！");
        }
        if(currTask != null) {
            List<SysUserDTO> sysUserList = flowableTaskMapper.findAssigneeByTaskId(taskId);
            Map<String, Object> jsonMap = new HashMap<>();
            jsonMap.put("processKey", processInstance.getProcessDefinitionKey());
            jsonMap.put("processInstanceId",currTask.getProcessInstanceId());
            JSONObject jsonParam = new JSONObject(jsonMap);
            String paramValue = jsonParam.toString();
            for (SysUserDTO user : sysUserList) {
                MessageChannelDTO messageChannel = new MessageChannelDTO();
                messageChannel.setMessageChannel(MessageChannelTypeConstant.PC);
                List<MessageChannelDTO> messageChannelList = new ArrayList<>();
                messageChannelList.add(messageChannel);
                MessageInfoDTO messageInfoDTO = messageUtil.createMessage(MessageTypeConstant.MY_NOTIFICATION,
                        "", "工作流【"+processInstance.getProcessDefinitionName()+currTask.getName()+"】，请您尽快处理！", message, user.getId(), SecurityUtil.getUserId(),
                        SecurityUtil.getUser().getUsername(), "businessFlowToDo",paramValue, messageChannelList);
                try {
                    messageUtil.sendMessage(messageInfoDTO);
                } catch (Exception e) {
                    log.error("工作流催办发送消息中心待办接口报错", e);
                }
            }
        }
        return R.ok("催办成功");
    }

    @Override
    public List<TaskDTO> getTaskByProcessInstanceId( String processInstanceId) {
        return flowableTaskMapper.getTaskByProcessInstanceId(processInstanceId);
    }

    @Override
    public TaskDTO findTaskByProcessInstanceId(String processInstanceId) {
        String userId = SecurityUtil.getUserId().toString();
        return flowableTaskMapper.findTaskByProcessInstanceId(processInstanceId,userId);
    }

}
