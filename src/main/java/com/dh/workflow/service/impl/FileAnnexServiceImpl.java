package com.dh.workflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.dto.bean.dto.workflow.vo.FileAnnexVo;
import com.dh.workflow.dao.FileAnnexMapper;
import com.dh.workflow.entity.FileAnnex;
import com.dh.workflow.service.IFileAnnexService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-29
 */
@Service
public class FileAnnexServiceImpl extends ServiceImpl<FileAnnexMapper, FileAnnex> implements IFileAnnexService {

    @Override
    public List<FileAnnex> selectFileAnnex(FileAnnex fileAnnex) {
        return list(new QueryWrapper<FileAnnex>().lambda().eq(FileAnnex::getFlowableTableId, fileAnnex.getFlowableTableId()).eq(FileAnnex::getTableType, fileAnnex.getTableType()));
    }

    @Override
    public List<FileAnnex> selectByTableId(String flowableTableId) {
        return list(new QueryWrapper<FileAnnex>().lambda().eq(FileAnnex::getFlowableTableId, flowableTableId));
    }

    @Override
    public List<FileAnnexVo> getListByTableIdAndType(Long tableId, Integer tableType) {
        return list(new QueryWrapper<FileAnnex>().lambda().eq(FileAnnex::getTableId, tableId).eq(tableType != null, FileAnnex::getTableType, tableType).eq(FileAnnex::getDelFlag, 0)).stream().map(entity -> {
            FileAnnexVo fileAnnexVo = new FileAnnexVo();
            BeanUtils.copyProperties(entity, fileAnnexVo);

            return fileAnnexVo;
        }).collect(Collectors.toList());
    }

}
