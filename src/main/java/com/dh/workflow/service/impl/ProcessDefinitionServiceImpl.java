package com.dh.workflow.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.common.util.R;
import com.dh.workflow.constant.FlowableConstant;
import com.dh.workflow.dao.FlowableProcessDefinitionMapper;
import com.dh.workflow.dto.FlowableProcessDefinitionDTO;
import com.dh.workflow.entity.FileAnnex;
import com.dh.workflow.service.BaseProcessService;
import com.dh.workflow.service.IFileAnnexService;
import com.dh.workflow.service.IFlowableProcessDefinitionService;
import com.dh.workflow.util.FileUtils;
import com.dh.workflow.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.job.api.Job;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.io.InputStream;
import java.util.*;
import java.util.zip.ZipInputStream;


/**
 * <AUTHOR>
 * @since 2020/7/16
 */
@Slf4j
@Service
@AllArgsConstructor
public class ProcessDefinitionServiceImpl extends BaseProcessService implements IFlowableProcessDefinitionService {

    private FlowableProcessDefinitionMapper flowableProcessDefinitionMapper;

    private IFileAnnexService fileAnnexService;

    private FileUtils fileUtils;

    @Override
    public IPage<FlowableProcessDefinitionDTO> getProcessDefinitionPage(Page<FlowableProcessDefinitionDTO> page,
                                                                        String name,String categoryId) {
        return flowableProcessDefinitionMapper.getProcessDefinitionPage(page,name,categoryId);
    }

    @Override
    public FlowableProcessDefinitionDTO getProcessDefinitionById(String processDefinitionId) {
        return flowableProcessDefinitionMapper.getById(processDefinitionId);
    }

    @Override
    public R doImport(String categoryId, MultipartFile file, FileAnnex fileAnnex) {
        String fileName = file.getOriginalFilename();
        try {
            InputStream fileInputStream = file.getInputStream();
            Deployment deployment = null;
            String extension = FilenameUtils.getExtension(fileName);
            if (extension.equals("zip") || extension.equals("bar")) {
                ZipInputStream zip = new ZipInputStream(fileInputStream);
                deployment = repositoryService.createDeployment().addZipInputStream(zip).deploy();
            } else if (extension.equals("png")) {
                deployment = repositoryService.createDeployment().addInputStream(fileName, fileInputStream).deploy();
            } else if (fileName.indexOf("bpmn20.xml") != -1) {
                deployment = repositoryService.createDeployment().addInputStream(fileName,fileInputStream).deploy();
            } else if (extension.equals("bpmn")) { // bpmn扩展名特殊处理，转换为bpmn20.xml
                String baseName = FilenameUtils.getBaseName(fileName);
                deployment = repositoryService.createDeployment().addInputStream(baseName + ".bpmn20.xml", fileInputStream).deploy();
            } else {
                fileUtils.sendDeleteFile(fileAnnex.getOssFolder(),fileAnnex.getFileName());
                return R.failed("不支持的文件类型"+extension);
            }
            List<ProcessDefinition> list = repositoryService.createProcessDefinitionQuery().deploymentId(deployment.getId()).list();
            if (list.size() == 0){
                fileUtils.sendDeleteFile(fileAnnex.getOssFolder(),fileAnnex.getFileName());
                return R.failed("流程部署失败,没有流程");
            }
            // 设置流程分类
            for (ProcessDefinition processDefinition : list) {
                if(!StringUtils.isEmpty(categoryId)) {
                    repositoryService.setProcessDefinitionCategory(processDefinition.getId(), categoryId);
                }
            }

            //保存附件
            fileAnnex.setTableType(0L);
            fileAnnex.setFlowableTableId(deployment.getId());
            fileAnnexService.save(fileAnnex);
        } catch (Exception e) {
            log.error("部署失败{}",e);
            fileUtils.sendDeleteFile(fileAnnex.getOssFolder(),fileAnnex.getFileName());
            return R.failed("流程部署失败!");
        }
        return R.ok("流程部署成功！");
    }

    @Override
    public R delete(String processDefinitionId, Boolean cascade) {
        FlowableProcessDefinitionDTO processDefinitionDTO = getProcessDefinitionById(processDefinitionId);
        if(processDefinitionDTO.getDeploymentId() == null){
            return R.failed("流程定义未发布!");
        }
        if(cascade){
            List<Job> jobs = managementService.createTimerJobQuery().processDefinitionId(processDefinitionId).list();
            for(Job job : jobs){
                managementService.deleteTimerJob(job.getId());
            }
            repositoryService.deleteDeployment(processDefinitionDTO.getDeploymentId(),true);
        }else{
            long processCount = runtimeService.createProcessInstanceQuery().processDefinitionId(processDefinitionId).count();
            if(processCount > 0){
                return R.failed("该流程定义有实例正在运行,不能删除!");
            }
            long jobCount = managementService.createTimerJobQuery().processDefinitionId(processDefinitionId).count();
            if(jobCount > 0 ){
                return R.failed("该流程定义有作业正在运行,不能删除!");
            }
            repositoryService.deleteDeployment(processDefinitionDTO.getDeploymentId());
        }
        return R.ok();
    }

    @Override
    public R activate(String processDefinitionId) {
        FlowableProcessDefinitionDTO processDefinition = getProcessDefinitionById(processDefinitionId);
        if(processDefinition.getSuspensionState() == FlowableConstant.ACTIVATE_STATE){
            return R.failed("流程已经激活！");
        }
        repositoryService.activateProcessDefinitionById(processDefinitionId,false,null);
        return R.ok();
    }

    @Override
    public R suspend(String processDefinitionId) {
        FlowableProcessDefinitionDTO processDefinition = getProcessDefinitionById(processDefinitionId);
        if(processDefinition.getSuspensionState() == FlowableConstant.SUSPENSION_STATE){
            return R.failed("流程已挂起!");
        }
        repositoryService.suspendProcessDefinitionById(processDefinitionId,false,null);
        return R.ok();
    }

    @Override
    public List<FlowableProcessDefinitionDTO> findProcessDefByCategoryId(String categoryId) {
        return flowableProcessDefinitionMapper.findProcessDefByCategoryId(categoryId);
    }
}
