package com.dh.workflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.workflow.bean.fm.GetWorkflowExtinfoFM;
import com.dh.workflow.bean.fm.WorkflowProcessCCNodeFM;
import com.dh.workflow.bean.fm.WorkflowProcessNodeFM;
import com.dh.workflow.bean.vo.WorkflowProcessCCNodeVO;
import com.dh.workflow.bean.vo.WorkflowProcessNodeVO;
import com.dh.workflow.config.RabbitMQConfig;
import com.dh.workflow.dao.WorkflowProcessNodeMapper;
import com.dh.workflow.entity.WorkflowProcessNodeEntity;
import com.dh.workflow.service.IWorkflowProcessNodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 工作流程节点服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/21
 */
@Slf4j
@Service
public class WorkflowProcessNodeServiceImpl extends ServiceImpl<WorkflowProcessNodeMapper, WorkflowProcessNodeEntity> implements IWorkflowProcessNodeService {

    @Resource
    RabbitMQConfig rabbitMQConfig;

    @Override
    public IPage<WorkflowProcessNodeVO> getNodeListById(Page<WorkflowProcessNodeVO> page, GetWorkflowExtinfoFM workflowExtinfoFM) {
        return this.baseMapper.getNodeListById(page,workflowExtinfoFM);
    }

    @Override
    @Transactional
    public String saveOrUpdateWorkflowProcessNode(WorkflowProcessNodeFM workflowProcessNodeFM) {
        String result = "";
        // 添加新数据
        WorkflowProcessNodeEntity workflowProcessNodeEntity = new WorkflowProcessNodeEntity();
        BeanUtils.copyProperties(workflowProcessNodeFM, workflowProcessNodeEntity);
        // 如果工作流审批节点有hook,和路由
        if (!StringUtils.isEmpty(workflowProcessNodeEntity.getHookName())) {
            String queueName = workflowProcessNodeEntity.getHookName() + ".queue";
            String routingKey = workflowProcessNodeEntity.getHookName() + ".routing";
            log.info("RabbitMQ exchange bind routing [{}] to queue [{}]", routingKey, queueName);
            rabbitMQConfig.dynamicBind(queueName, routingKey);
        }
        if(workflowProcessNodeEntity.getId() != null) {
            // 更新操作时强制更新 null 值字段
            UpdateWrapper<WorkflowProcessNodeEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper
                    .eq("id", workflowProcessNodeEntity.getId())
                    .set(workflowProcessNodeFM.getProcessId() != null, "process_id", workflowProcessNodeFM.getProcessId())
                    .set(workflowProcessNodeFM.getExtId() != null, "ext_id", workflowProcessNodeFM.getExtId())
                    .set(workflowProcessNodeFM.getNodeName() != null, "node_name", workflowProcessNodeFM.getNodeName())
                    .set("deadline_by", workflowProcessNodeFM.getDeadlineBy() == null ? null : workflowProcessNodeFM.getDeadlineBy())
                    .set("deadline_post", workflowProcessNodeFM.getDeadlinePost() == null ? null : workflowProcessNodeFM.getDeadlinePost())
                    .set(workflowProcessNodeFM.getType() != null, "type", workflowProcessNodeFM.getType())
                    .set(workflowProcessNodeFM.getStatusCode() != null, "status_code", workflowProcessNodeFM.getStatusCode())
                    .set(workflowProcessNodeFM.getStatusText() != null, "status_text", workflowProcessNodeFM.getStatusText())
                    .set(workflowProcessNodeFM.getHookName() != null, "hook_name", workflowProcessNodeFM.getHookName())
                    .set( "deadline_by_user_ids", workflowProcessNodeFM.getDeadlineByUserIds() == null ? null : workflowProcessNodeFM.getDeadlineByUserIds());

            this.update(updateWrapper);
        } else {
            this.save(workflowProcessNodeEntity);
        }
        return result;
    }

    @Override
    public String removeWorkflowProcessNode(Long id) {
        String result = "";
        this.removeById(id);
        return result;
    }

    @Override
    public WorkflowProcessCCNodeVO getCCInfoById(Long processId, Long extId) {
        return this.baseMapper.getCCInfoById(processId,extId);
    }

    @Override
    public String removeWorkflowProcessCCNode(Long processId, Integer extId) {
        String result = "";
        this.remove(new UpdateWrapper<WorkflowProcessNodeEntity>().eq("process_id", processId).eq("ext_id", extId).eq("is_approver_config", 1));
        return result;
    }

    @Override
    @Transactional
    public String saveOrUpdateWorkflowProcessCCNode(WorkflowProcessCCNodeFM workflowProcessNodeFM) {
        String result = "";
        // 删除旧的抄送人信息
        this.removeWorkflowProcessCCNode(workflowProcessNodeFM.getProcessId(), workflowProcessNodeFM.getExtId());
        // 添加新的抄送人信息
        if (!CollectionUtils.isEmpty(workflowProcessNodeFM.getNodeList())) {
            List<WorkflowProcessNodeEntity> workflowProcessNodeEntityList = workflowProcessNodeFM.getNodeList().stream().map(ccConfigVO -> {
                WorkflowProcessNodeEntity workflowProcessNodeEntity = new WorkflowProcessNodeEntity();
                BeanUtils.copyProperties(ccConfigVO, workflowProcessNodeEntity);
                workflowProcessNodeEntity.setProcessId(workflowProcessNodeFM.getProcessId());
                workflowProcessNodeEntity.setExtId(workflowProcessNodeFM.getExtId());
                workflowProcessNodeEntity.setNodeName(workflowProcessNodeFM.getNodeName());
                workflowProcessNodeEntity.setIsApproverConfig(workflowProcessNodeFM.getIsApproverConfig());
                return workflowProcessNodeEntity;
            }).collect(Collectors.toList());
            this.saveOrUpdateBatch(workflowProcessNodeEntityList);
        }
        return result;
    }
}
