package com.dh.workflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.dto.bean.dto.workflow.UserDTO;
import com.dh.framework.security.util.SecurityUtil;
import com.dh.workflow.dao.MessageInfoMapper;
import com.dh.workflow.dao.SysUserMapper;
import com.dh.workflow.dao.WorkflowProcessMapper;
import com.dh.workflow.entity.*;
import com.dh.workflow.service.MessageInfoService;
import com.dh.workflow.service.MessageUrlService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 消息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-11
 */
@Slf4j
@Service
public class MessageInfoServiceImpl extends ServiceImpl<MessageInfoMapper, MessageInfo> implements MessageInfoService {
    //    @Resource
//    private MessageInfoMapper messageInfoMapper;
    @Resource
    private MessageUrlService messageUrlService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private WorkflowProcessMapper workflowProcessMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    /**
     * 添加代办事项
     *
     * @param userList            用户列表
     * @param workflowProcessInst 流程实例
     * @param msgUrl              代办的url
     */
    @Override
    @Transactional
    public void addTodo(List<UserDTO> userList, WorkflowProcessInstEntity workflowProcessInst,
                        WorkflowProcessEntity processEntity, String msgUrl) {

        log.debug("Workflow inst {} add todos", workflowProcessInst.getId());

        HashMap<String, Long> param = new HashMap<>();
        param.put("id", workflowProcessInst.getBusinessId());
//            param.put("processId", workflowProcessInst.getProcessId());
//            param.put("extId", workflowProcessInst.getExtId());
        // 查询messageUrl表中是否有该url记录
        MessageUrl result = messageUrlService.getOne(new LambdaQueryWrapper<MessageUrl>().eq(MessageUrl::getMsgUrl, msgUrl));
        if (result == null) {
            MessageUrl messageUrl = new MessageUrl();
            messageUrl.setMsgUrl(msgUrl);
            messageUrl.setUrlRemarks(processEntity.getName() + "待办");
            messageUrlService.save(messageUrl);
        }

        userList.forEach(userVo -> {
            log.debug("Workflow inst {} add user {} todo", workflowProcessInst.getId(), userVo.getRealName());

            MessageInfo messageInfo = new MessageInfo();
            messageInfo.setMsgType(1);
            messageInfo.setBusinessId(workflowProcessInst.getBusinessId().toString());
            messageInfo.setBusinessType(processEntity.getKey());
            messageInfo.setSubject("您有一条" + processEntity.getName() + "待审批，请前往审批");
            messageInfo.setReceiveTo(userVo.getUserId());
            messageInfo.setMsgUrl(msgUrl);
            messageInfo.setCreateBy(SecurityUtil.getUserId());
            messageInfo.setCreateByName(SecurityUtil.getUniqueName());
            messageInfo.setCreateDate(LocalDateTime.now());
            try {
                messageInfo.setParamsValue(objectMapper.writeValueAsString(param));
            } catch (Exception e) {
                log.error("参数转换异常", e);
            }

            this.save(messageInfo);
        });
    }

    /**
     * 设置代办事项为已办
     *
     * @param businessId   业务ID
     * @param businessType 代办类型
     * @return true: 成功，false: 失败
     */
    @Override
    public boolean doTodo(String businessId, String businessType) {
        return this.lambdaUpdate().eq(MessageInfo::getBusinessId, businessId)
                .eq(MessageInfo::getBusinessType, businessType)
                .eq(MessageInfo::getMsgType, 1)
                .eq(MessageInfo::getHandleFlag, 0)
                .set(MessageInfo::getReadFlag, 1)
                .set(MessageInfo::getHandleFlag, 1)
                .update();
    }

    /**
     * 审批通过给抄送人发送待办消息
     * @Author: 武琦超
     * @Date: 2025/7/17 9:34
     * @param userIds 用户id字符串
     * @param workflowProcessEntity 工作流对象
     * @return: null
     **/
    @Override
    public void sendToDoMessage(String userIds, WorkflowProcessEntity workflowProcessEntity,WorkflowProcessInstEntity workflowProcessInst) {
        HashMap<String, Long> param = new HashMap<>();
        param.put("id", workflowProcessInst.getBusinessId());
        SysUser sysUser = sysUserMapper.selectById(workflowProcessInst.getCreatedBy());
        List<Long> userIdList = Arrays.stream(userIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        userIdList.forEach(userId -> {
            MessageInfo messageInfo = new MessageInfo();
            messageInfo.setMsgType(1);
            messageInfo.setBusinessId(workflowProcessInst.getBusinessId().toString());
            messageInfo.setBusinessType(workflowProcessEntity.getKey());
            messageInfo.setSubject(sysUser.getRealName() + "发起的" + workflowProcessEntity.getName() + "审批，已审批通过。");
            messageInfo.setReceiveTo(userId);
            messageInfo.setMsgUrl(workflowProcessEntity.getTodoRoute());
            messageInfo.setCreateBy(SecurityUtil.getUserId());
            messageInfo.setCreateByName(SecurityUtil.getUniqueName());
            messageInfo.setCreateDate(LocalDateTime.now());
            try {
                messageInfo.setParamsValue(objectMapper.writeValueAsString(param));
            } catch (Exception e) {
                log.error("参数转换异常", e);
            }
            this.save(messageInfo);
        });
    }

    /**
     * 设置待办为已办
     * @Author: 武琦超
     * @Date: 2025/7/17 9:51
     * @return: null
     **/
    @Override
    public boolean doToDoMessage(WorkflowProcessInstEntity workflowProcessInstEntity, WorkflowProcessEntity workflowProcessEntity) {
        Long userId = SecurityUtil.getUserId();
        return this.lambdaUpdate().eq(MessageInfo::getBusinessId, workflowProcessInstEntity.getBusinessId())
                .eq(MessageInfo::getBusinessType, workflowProcessEntity.getKey())
                .eq(MessageInfo::getReceiveTo, userId)
                .eq(MessageInfo::getMsgType, 1)
                .eq(MessageInfo::getHandleFlag, 0)
                .set(MessageInfo::getReadFlag, 1)
                .set(MessageInfo::getHandleFlag, 1)
                .update();
    }
}