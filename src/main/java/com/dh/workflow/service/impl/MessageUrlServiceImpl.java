package com.dh.workflow.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.workflow.dao.MessageUrlMapper;
import com.dh.workflow.entity.MessageUrl;
import com.dh.workflow.service.MessageUrlService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 非点击消除代办URL配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/30
 */
@Service
public class MessageUrlServiceImpl extends ServiceImpl<MessageUrlMapper, MessageUrl> implements MessageUrlService {
}
