package com.dh.workflow.service.impl;

import com.dh.common.util.R;
import com.dh.dto.bean.dto.workflow.*;
import com.dh.dto.feign.FlowFeignService;
import com.dh.framework.security.exception.BusinessException;
import com.dh.workflow.service.IWorkflowService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 工作流服务远程调用
 *
 * <AUTHOR>
 * @since 2025-05-23
 */
@RestController
@Api(tags = "工作流服务远程调用feign")
@Slf4j
@RequestMapping("/feign")
public class FlowFeignServiceImpl implements FlowFeignService {
    @Resource
    IWorkflowService workflowService;

    @Override
    public R<WorkflowInfoDTO> getTask(FlowTaskDTO flowTaskDTO) {
        WorkflowInfoDTO workflowInfoDTO = workflowService.getTaskInfo(flowTaskDTO, false);
        if (CollectionUtils.isEmpty(workflowInfoDTO.getNodeList()) || CollectionUtils.isEmpty(workflowInfoDTO.getProcedureList()) || workflowInfoDTO.getInstance() == null) {
            return R.failed("任务不存在");
        }
        return R.ok(workflowInfoDTO);
    }

    @Override
    public R<ApprovalRespDTO> approvalTask(ApproveReqDTO flowTaskDTO) {
        ApprovalRespDTO approveRespDTO = workflowService.approvalTask(flowTaskDTO);

        return R.ok(approveRespDTO);
    }

    @Override
    public R<CreateRespDTO> createTask(FlowTaskDTO flowTaskDTO) {
        try {
            CreateRespDTO node = workflowService.createTask(flowTaskDTO);

            return R.ok(node);
        } catch (BusinessException exception) {
            log.error("创建任务流程失败: {}", exception.getMessage());
            return R.failed(exception.getMessage());
        }
    }

    @Override
    public R<Boolean> transferTask(TransferTaskDTO transferTaskDTO) {
        try {
            Boolean status = workflowService.transferTask(transferTaskDTO);

            return R.ok(status);
        } catch (BusinessException exception) {
            log.error("移交任务失败: {}", exception.getMessage());
            return R.failed(exception.getMessage());
        }
    }
}
