package com.dh.workflow.service.impl;

import com.dh.workflow.cmd.AddHisCommentCmd;
import com.dh.workflow.constant.CommentTypeEnum;
import com.dh.workflow.dao.FlowableCommentMapper;
import com.dh.workflow.dto.CommentDTO;
import com.dh.workflow.service.BaseProcessService;
import com.dh.workflow.service.IFlowableCommentService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/8
 */
@Service
@AllArgsConstructor
public class FlowableCommentServiceImpl extends BaseProcessService implements IFlowableCommentService {

    private FlowableCommentMapper flowableCommentMapper;

    @Override
    public void addComment(CommentDTO comment) {
        managementService.executeCommand(new AddHisCommentCmd(comment.getTaskId(), comment.getUserId(), comment.getProcessInstanceId(),
                comment.getType(), comment.getMessage()));
    }

    @Override
    public List<CommentDTO> getFlowCommentVosByProcessInstanceId(String processInstanceId) {

        List<CommentDTO> datas = flowableCommentMapper.getFlowCommentVosByProcessInstanceId(processInstanceId);
        datas.forEach(CommentDto -> {
            CommentDto.setTypeName(CommentTypeEnum.getEnumMsgByType(CommentDto.getType()));
        });
        return datas;
    }
}
