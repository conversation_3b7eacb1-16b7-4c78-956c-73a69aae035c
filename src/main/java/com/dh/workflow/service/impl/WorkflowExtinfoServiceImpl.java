package com.dh.workflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.workflow.bean.fm.WorkflowExtinfoFM;
import com.dh.workflow.bean.vo.WorkflowExtinfoVO;
import com.dh.workflow.dao.WorkflowExtinfoMapper;
import com.dh.workflow.dao.WorkflowProcessInstMapper;
import com.dh.workflow.entity.WorkflowExtinfo;
import com.dh.workflow.entity.WorkflowProcessInstEntity;
import com.dh.workflow.service.IWorkflowExtinfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 工作流扩展信息服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Slf4j
@Service
public class WorkflowExtinfoServiceImpl extends ServiceImpl<WorkflowExtinfoMapper, WorkflowExtinfo> implements IWorkflowExtinfoService {

    @Resource
    private WorkflowProcessInstMapper workflowProcessInstMapper;

    @Override
    public List<WorkflowExtinfoVO> getExtinfoList(Long id) {
        return this.baseMapper.getExtinfoList(id);
    }

    @Override
    public String removeWorkflowExtinfo(Long id) {
        String result = "";
        WorkflowExtinfo workflowExtinfo = this.getById(id);
        if (!Objects.isNull(workflowExtinfo)) {
            Integer count = workflowProcessInstMapper.selectCount(new LambdaQueryWrapper<WorkflowProcessInstEntity>()
                    .eq(WorkflowProcessInstEntity::getProcessId, workflowExtinfo.getProcessId())
                    .eq(WorkflowProcessInstEntity::getExtId, workflowExtinfo.getExtId()));
            if (count > 0) {
                return "扩展流程正在使用，无法删除";
            }
        }
        this.removeById(id);
        return result;
    }

    @Override
    public String saveOrUpdateExtinfo(WorkflowExtinfoFM workflowExtinfoFM) {
        String result = "";
        if (!StringUtils.isEmpty(workflowExtinfoFM.getTitle())) {
            List<WorkflowExtinfo> workflowExtinfoList = this.baseMapper.selectList(
                    new LambdaQueryWrapper<WorkflowExtinfo>()
                            .eq(WorkflowExtinfo::getProcessId, workflowExtinfoFM.getProcessId())
                            .eq(WorkflowExtinfo::getTitle, workflowExtinfoFM.getTitle())
                            .ne(WorkflowExtinfo::getProcessId, workflowExtinfoFM.getProcessId()));
            if (!CollectionUtils.isEmpty(workflowExtinfoList)) {
                return "相同工作流下扩展内容标题不能相同";
            }
        }
        if (!StringUtils.isEmpty(workflowExtinfoFM.getExtId())) {
            List<WorkflowExtinfo> workflowExtinfoList = this.baseMapper.selectList(
                    new LambdaQueryWrapper<WorkflowExtinfo>()
                            .eq(WorkflowExtinfo::getProcessId, workflowExtinfoFM.getProcessId())
                            .eq(WorkflowExtinfo::getExtId, workflowExtinfoFM.getExtId())
                            .ne(WorkflowExtinfo::getProcessId, workflowExtinfoFM.getProcessId()));
            if (!CollectionUtils.isEmpty(workflowExtinfoList)) {
                return "相同工作流下扩展id不能相同";
            }
        }
        WorkflowExtinfo workflowExtinfo = new WorkflowExtinfo();
        BeanUtils.copyProperties(workflowExtinfoFM, workflowExtinfo);
        this.saveOrUpdate(workflowExtinfo);
        return result;
    }
}
