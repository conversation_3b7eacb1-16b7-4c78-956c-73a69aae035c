package com.dh.workflow.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dh.dto.bean.dto.file.InfraFileDTO;
import com.dh.dto.bean.dto.workflow.*;
import com.dh.dto.bean.dto.workflow.UserDTO;
import com.dh.dto.bean.dto.workflow.enums.TaskResultEnum;
import com.dh.dto.bean.dto.workflow.vo.FileAnnexVo;
import com.dh.dto.bean.dto.workflow.vo.WorkflowProcedureVo;
import com.dh.dto.bean.dto.workflow.vo.WorkflowProcessInstVo;
import com.dh.dto.bean.dto.workflow.vo.WorkflowProcessNodeVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.framework.security.exception.BusinessException;
import com.dh.workflow.bean.fm.GetWorkflowProcessFM;
import com.dh.workflow.bean.fm.WorkflowProcessFM;
import com.dh.workflow.bean.vo.WorkflowProcessVO;
import com.dh.workflow.bean.vo.WorkflowTreeListVO;
import com.dh.workflow.config.RabbitMQConfig;
import com.dh.workflow.constant.FlowTypeEnum;
import com.dh.workflow.constant.StatusEnum;
import com.dh.workflow.dao.*;
import com.dh.workflow.entity.*;
import com.dh.workflow.service.*;
import com.dh.workflow.util.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程服务实现类
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Slf4j
@Service
public class WorkflowServiceImpl extends ServiceImpl<WorkflowProcessMapper, WorkflowProcessEntity> implements IWorkflowService {
    /**
     * 工作流相关附件表类型
     */
    public static final Integer WORKFLOW_TABLE_TYPE = 20;

    @Resource
    private WorkflowProcessMapper workflowProcessMapper;

    @Resource
    private WorkflowProcessNodeMapper workflowProcessNodeMapper;

    @Resource
    private WorkflowProcedureMapper workflowProcedureMapper;

    @Resource
    private WorkflowProcessInstMapper workflowProcessInstMapper;

    @Resource
    private HookProducer hookProducer;

    @Resource
    private IWorkflowExtinfoService workflowExtinfoService;

    /**
     * 创建流程计算工厂
     */
    @Resource
    private DeadlineCalcFactory deadlineCalcFactory;

    @Resource
    private WorkflowExtinfoMapper workflowExtinfoMapper;

    @Resource
    private IFileAnnexService fileAnnexService;

    @Resource
    private MessageInfoService messageInfoService;

    @Resource
    private RabbitMQConfig rabbitMQConfig;

    /**
     * 创建流程节点
     *
     * @param workflowProcessInstEntity 流程实例
     * @param node                      流程节点
     */
    private WorkflowProcedureEntity createProcedure(WorkflowProcessInstEntity workflowProcessInstEntity, WorkflowProcessNodeEntity node) {
        return this.createProcedure(workflowProcessInstEntity, node, false);
    }

    /**
     * 创建流程节点
     *
     * @param workflowProcessInstEntity 流程实例
     * @param node                      流程节点
     * @param isJumper                  是否跳过节点
     */
    private WorkflowProcedureEntity createProcedure(WorkflowProcessInstEntity workflowProcessInstEntity, WorkflowProcessNodeEntity node, boolean isJumper) {
        Long createdId = SecurityUtil.getUserId();

        WorkflowProcedureEntity workflowProcedureEntity = new WorkflowProcedureEntity();

        workflowProcedureEntity.setProcessNodeId(node.getId());
        workflowProcedureEntity.setProcessInstId(0L);
        workflowProcedureEntity.setStartDate(LocalDateTime.now());
        workflowProcedureEntity.setDeadlineDate(LocalDateTime.now());
        workflowProcedureEntity.setProcessInstId(workflowProcessInstEntity.getId());
        WorkflowProcessNodeDTO workflowProcessNodeDTO = new WorkflowProcessNodeDTO();
        BeanUtils.copyProperties(node, workflowProcessNodeDTO);
        // 非跳过节点
        if (FlowTypeEnum.None.getCode() != workflowProcessNodeDTO.getType()) {
            DeadlineCalc deadlineCalc = this.deadlineCalcFactory.create(workflowProcessNodeDTO);
            List<UserDTO> userDtos = deadlineCalc.calc(workflowProcessInstEntity, workflowProcessNodeDTO, workflowProcedureEntity);
            if (CollectionUtils.isEmpty(userDtos)) {
                log.error("workflowProcessNode => {}", workflowProcessNodeDTO);
                throw new BusinessException("未找到对应的节点审批人，请确认节点审批人配置是否正确");
            }
            String ids = userDtos.stream().map(user -> String.valueOf(user.getUserId())).collect(Collectors.joining(","));
            log.info(">>>>>>>>> {} - ids: {}", userDtos, ids);
            workflowProcedureEntity.setCurrentProcessUsers(ids);

            //添加代办事项(先完成之前的代办事项)
            WorkflowProcessEntity processEntity = workflowProcessMapper.selectById(workflowProcessInstEntity.getProcessId());
            if (processEntity.getIsTodo() == 1) {
                messageInfoService.doTodo(workflowProcessInstEntity.getBusinessId().toString(), processEntity.getKey());
                messageInfoService.addTodo(userDtos, workflowProcessInstEntity, processEntity, processEntity.getTodoRoute());
            }
        }

        if (isJumper) {
            workflowProcedureEntity.setDeadlineBy(createdId);
            workflowProcedureEntity.setEndDate(LocalDateTime.now());
            workflowProcedureEntity.setDeadlineResult(TaskResultEnum.INIT.toString());
            workflowProcedureEntity.setDeadlineSuggestion("");
        }

        workflowProcedureMapper.insert(workflowProcedureEntity);

        return workflowProcedureEntity;
    }

    /**
     * 查找下级节点
     *
     * @param procedureList 当前已执行节点
     * @param nodeList      任务全部节点
     * @return 已执行的下级节点
     */
    private WorkflowProcessNodeEntity findFirstMissingNode(List<WorkflowProcedureEntity> procedureList, List<WorkflowProcessNodeEntity> nodeList) {

        if (procedureList == null || procedureList.isEmpty() || nodeList == null) {
            return null;
        }

        Set<Long> nodeIdSet = procedureList.stream().map(WorkflowProcedureEntity::getProcessNodeId).collect(Collectors.toSet());

        return nodeList.stream().filter(node -> !nodeIdSet.contains(node.getId()) && node.getIsApproverConfig() == 0).findFirst().orElse(null);
    }

    /**
     * 判断是否为当前处理人
     *
     * @param ids 处理人列表
     * @param id  当前登录用户
     * @return 是否为当前处理人
     */
    private boolean checkProcessUser(String ids, Long id) {
        Set<Long> idSet = Arrays.stream(ids.split(",")).map(String::trim).filter(s -> !s.isEmpty()).map(Long::valueOf).collect(Collectors.toSet());

        if (idSet.contains(id)) {
            log.info("包含该ID！");
            return true;
        } else {
            log.info("当前处理人不包含该ID！");
            return false;
        }
    }

    /**
     * 获取流程实例
     *
     * @param processId  流程id
     * @param businessId 业务id
     * @return 流程实例
     */
    private WorkflowProcessInstEntity getProcessInst(Long processId, Long businessId) {
        LambdaQueryWrapper<WorkflowProcessInstEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkflowProcessInstEntity::getProcessId, processId);
        queryWrapper.eq(WorkflowProcessInstEntity::getBusinessId, businessId);
        queryWrapper.notIn(WorkflowProcessInstEntity::getStatusCode, StatusEnum.REFUSE.getCode(), StatusEnum.REVOKE.getCode(), StatusEnum.FINISHED.getCode());

        return workflowProcessInstMapper.selectOne(queryWrapper);
    }

    private WorkflowProcessInstEntity getProcessInst(Long instanceId) {
        return workflowProcessInstMapper.selectById(instanceId);
    }

    /**
     * 检测流程实例是否存在
     *
     * @param processId  工作流ID
     * @param businessId 业务ID
     * @param instanceId 流程实例ID
     * @return 流程实例
     */
    private WorkflowProcessInstEntity checkProcessInst(Long processId, Long businessId, Long instanceId, String key) {
        WorkflowProcessInstEntity workflowProcessInstEntity;
        if (!StringUtils.isEmpty(key)) {
            WorkflowProcessEntity workflowProcessEntity = getProcessEntity(key);
            processId = workflowProcessEntity.getId();
        }
        if (instanceId == null) {
            workflowProcessInstEntity = getProcessInst(processId, businessId);
        } else {
            workflowProcessInstEntity = getProcessInst(instanceId);
        }

        if (workflowProcessInstEntity == null) {
            throw new BusinessException("流程已完成，无法再次审批");
        }

        int[] needStatus = {StatusEnum.REVOKE.getCode(), StatusEnum.REFUSE.getCode(), StatusEnum.FINISHED.getCode()};
        if (Arrays.stream(needStatus).anyMatch(day -> day == workflowProcessInstEntity.getStatusCode())) {
            throw new BusinessException("流程已完成，无法再次审批");
        }

        return workflowProcessInstEntity;
    }

    /**
     * 流程完成后发送消息
     *
     * @param taskResult                流程结果
     * @param workflowProcessInstEntity 流程实例
     * @param workflowProcedureEntity   流程节点
     */
    private void sendCompletedMessage(TaskResultEnum taskResult,
                                      WorkflowProcessInstEntity workflowProcessInstEntity,
                                      WorkflowProcedureEntity workflowProcedureEntity) {
        WorkflowProcessEntity workflowProcessEntity = getProcessEntity(workflowProcessInstEntity.getProcessId());
        HookCompletedDataDTO hookDataDTO = new HookCompletedDataDTO();
        hookDataDTO.setTaskResultEnum(taskResult);
        WorkflowProcessInstVo workflowProcessInstVo = new WorkflowProcessInstVo();
        BeanUtils.copyProperties(workflowProcessInstEntity, workflowProcessInstVo);
        WorkflowProcedureVo workflowProcedureVo = new WorkflowProcedureVo();
        BeanUtils.copyProperties(workflowProcedureEntity, workflowProcedureVo);
        hookDataDTO.setWorkflowProcessInst(workflowProcessInstVo);
        hookDataDTO.setWorkflowProcedure(workflowProcedureVo);

        hookDataDTO.setWorkflowProcessInst(workflowProcessInstVo);
        hookDataDTO.setWorkflowProcedure(workflowProcedureVo);

        this.hookProducer.sendCompletedMessage(hookDataDTO, workflowProcessEntity.getHookName());
    }

    /**
     * 获得流程实体
     *
     * @param id 流程id
     * @return 流程实体
     */
    public WorkflowProcessEntity getProcessEntity(Long id) {
        return workflowProcessMapper.selectById(id);
    }

    /**
     * 获得流程实体按key查找
     *
     * @param key 流程Key
     * @return 流程实体
     */
    public WorkflowProcessEntity getProcessEntity(String key) {
        LambdaQueryWrapper<WorkflowProcessEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkflowProcessEntity::getKey, key);

        return workflowProcessMapper.selectOne(queryWrapper);
    }

    /**
     * 获得流程节点列表
     *
     * @param id    processId
     * @param extId 扩展Id default: 0
     * @return 流程节点List
     */
    @Override
    public List<WorkflowProcessNodeEntity> getProcessNodes(Long id, Long extId) {
        LambdaQueryWrapper<WorkflowProcessNodeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkflowProcessNodeEntity::getProcessId, id);
        queryWrapper.eq(WorkflowProcessNodeEntity::getExtId, extId);
        queryWrapper.orderByAsc(WorkflowProcessNodeEntity::getIsApproverConfig);

        return workflowProcessNodeMapper.selectList(queryWrapper);
    }

    /**
     * 初始化表单数据
     *
     * @param workflowProcessInstVo 流程实例
     */
    private void initFormList(WorkflowProcessInstVo workflowProcessInstVo)
    {
        if (workflowProcessInstVo.getParams().containsKey("@data")) {
            String data = workflowProcessInstVo.getParams().getOrDefault("@data", null);
            if (data != null) {
                try {
                    List<FormItemDTO> formItemDTOList = JSONUtil.toList(data, FormItemDTO.class);

                    formItemDTOList = formItemDTOList.stream().map(formItemDTO -> {
                        if (formItemDTO.getType() == 1) {
                            if (formItemDTO.getValue() != null && !formItemDTO.getValue().isEmpty()) {
                                List<Long> fileIds = Arrays.stream(formItemDTO.getValue().split(","))
                                        .map(Long::valueOf)
                                        .collect(Collectors.toList());
                                formItemDTO.setInfraFileDTOList(this.getFormItemFiles(fileIds));
                            }
                        }

                        return formItemDTO;
                    }).collect(Collectors.toList());

                    workflowProcessInstVo.setFormItemDTOList(formItemDTOList);
                } catch (Exception ex) {
                    log.error("反序列化对象失败.", ex);
                }
            }
        }
    }

    /**
     * 获取任务信息
     *
     * @param flowTaskDTO 流程任务DTO
     * @return 任务信息
     */
    @Override
    public WorkflowInfoDTO getTaskInfo(FlowTaskDTO flowTaskDTO, boolean isHistory) {
        log.info("ddd {}", flowTaskDTO.getInstanceId());
        LambdaQueryWrapper<WorkflowProcessInstEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (flowTaskDTO.getKey() != null && !flowTaskDTO.getKey().isEmpty()) {
            WorkflowProcessEntity workflowProcessEntity = getProcessEntity(flowTaskDTO.getKey());
            flowTaskDTO.setProcessId(workflowProcessEntity.getId());
        }
        if (flowTaskDTO.getInstanceId() != null) {
            queryWrapper.eq(WorkflowProcessInstEntity::getId, flowTaskDTO.getInstanceId());
        } else {
            queryWrapper.eq(WorkflowProcessInstEntity::getProcessId, flowTaskDTO.getProcessId());
            queryWrapper.eq(WorkflowProcessInstEntity::getBusinessId, flowTaskDTO.getBusinessId());
            queryWrapper.eq(WorkflowProcessInstEntity::getExtId, flowTaskDTO.getExtId());
        }
        queryWrapper.orderByDesc(WorkflowProcessInstEntity::getCreatedAt);
        List<WorkflowProcessInstEntity> workflowProcessInstEntityList = workflowProcessInstMapper.selectList(queryWrapper);
        if (workflowProcessInstEntityList.isEmpty()) {
            return new WorkflowInfoDTO();
        }
        WorkflowProcessInstEntity workflowProcessInstEntity = workflowProcessInstEntityList.get(0);

        LambdaQueryWrapper<WorkflowProcedureEntity> queryProcedureWrapper = new LambdaQueryWrapper<>();
        queryProcedureWrapper.eq(WorkflowProcedureEntity::getProcessInstId, workflowProcessInstEntity.getId());

        List<WorkflowProcessNodeEntity> workflowProcessNodeEntityList = getProcessNodes(workflowProcessInstEntity.getProcessId(), workflowProcessInstEntity.getExtId());
        List<WorkflowProcedureDTO> workflowProcedureEntityList;
        if (isHistory) {
            LambdaQueryWrapper<WorkflowProcessInstEntity> subQueryWrapper = new LambdaQueryWrapper<>();
            subQueryWrapper.eq(WorkflowProcessInstEntity::getProcessId, workflowProcessInstEntity.getProcessId());
            subQueryWrapper.eq(WorkflowProcessInstEntity::getBusinessId, workflowProcessInstEntity.getBusinessId());
            List<Long> instIds = workflowProcessInstMapper.selectList(subQueryWrapper)
                    .stream()
                    .map(WorkflowProcessInstEntity::getId)
                    .collect(Collectors.toList());
//            log.info(">>>>> {}", instIds);
            workflowProcedureEntityList = workflowProcedureMapper.queryProcedureHistory(instIds);
        } else {
            workflowProcedureEntityList = workflowProcedureMapper.queryProcedureList(workflowProcessInstEntity.getId());
        }

        WorkflowProcessInstVo workflowProcessInstVo = new WorkflowProcessInstVo();
        BeanUtils.copyProperties(workflowProcessInstEntity, workflowProcessInstVo);

        List<WorkflowProcessNodeVo> workflowProcessNodeVoList = workflowProcessNodeEntityList.stream()
                .map(entity -> {
                    WorkflowProcessNodeDTO workflowProcessNodeDTO = new WorkflowProcessNodeDTO();
                    WorkflowProcessNodeVo vo = new WorkflowProcessNodeVo();
                    BeanUtils.copyProperties(entity, vo);
                    BeanUtils.copyProperties(entity, workflowProcessNodeDTO);
                    if (entity.getType() != 0) {
                        DeadlineCalc deadlineCalc = deadlineCalcFactory.create(workflowProcessNodeDTO);
                        List<UserDTO> userDTOS = deadlineCalc.calc(workflowProcessInstEntity, workflowProcessNodeDTO, null);
                        if (CollectionUtils.isEmpty(userDTOS)) {
                            log.error("flowTaskDTO => {},workflowProcessNode => {}", flowTaskDTO, workflowProcessNodeDTO);
                            throw new BusinessException("未找到对应的节点审批人，请确认节点审批人配置是否正确");
                        }
                        String ids = userDTOS.stream().map(user -> String.valueOf(user.getUserId())).collect(Collectors.joining(","));
                        String names = userDTOS.stream().map(UserDTO::getRealName).collect(Collectors.joining(","));
                        vo.setDeadlineBy(userDTOS.isEmpty() ? null : ids);
                        vo.setDeadlineName(userDTOS.isEmpty() ? null : names);
                        vo.setUserList(userDTOS);
                    }
                    return vo;
                })
                .collect(Collectors.toList());
        // 处理抄送人的数据
        workflowProcessNodeVoList = this.extracted(workflowProcessNodeVoList);

        List<WorkflowProcedureVo> workflowProcedureVoList = workflowProcedureEntityList.stream()
                .map(entity -> {
                    WorkflowProcedureVo vo = new WorkflowProcedureVo();
                    BeanUtils.copyProperties(entity, vo);
                    List<FileAnnexVo> annexes = this.fileAnnexService.getListByTableIdAndType(vo.getId(), WORKFLOW_TABLE_TYPE);
                    vo.setAttachments(annexes);
                    return vo;
                })
                .collect(Collectors.toList());

        // 查询task时，如果审批已结束，则将对应的抄送人待办消除
        if (workflowProcessInstVo.getStatusCode() == 99) {
            this.doTodo(workflowProcessInstVo.getId());
        }

        initFormList(workflowProcessInstVo);

        return new WorkflowInfoDTO(workflowProcessInstVo, workflowProcessNodeVoList, workflowProcedureVoList);
    }

    private List<WorkflowProcessNodeVo> extracted(List<WorkflowProcessNodeVo> workflowProcessNodeVoList) {
        // 1. 分离抄送人节点和其他节点
        Map<Boolean, List<WorkflowProcessNodeVo>> partitionedNodes = workflowProcessNodeVoList.stream()
                .collect(Collectors.partitioningBy(vo -> vo.getIsApproverConfig() == 1));

        List<WorkflowProcessNodeVo> approverNodes = partitionedNodes.get(true);
        List<WorkflowProcessNodeVo> otherNodes = partitionedNodes.get(false);

        if (approverNodes.isEmpty()) {
            return workflowProcessNodeVoList;
        }

        // 2. 创建合并后的抄送人节点
        WorkflowProcessNodeVo mergedNode = new WorkflowProcessNodeVo();
        mergedNode.setProcessId(workflowProcessNodeVoList.get(0).getProcessId());
        mergedNode.setExtId(workflowProcessNodeVoList.get(0).getExtId());
        mergedNode.setNodeName("抄送人");
        mergedNode.setIsApproverConfig(1);
        mergedNode.setUserList(approverNodes.stream()
                .flatMap(vo -> vo.getUserList().stream())
                .distinct()
                .collect(Collectors.toList()));

        mergedNode.setDeadlineBy(approverNodes.stream()
                .map(WorkflowProcessNodeVo::getDeadlineBy)
                .filter(Objects::nonNull)
                .flatMap(ids -> Arrays.stream(ids.split(",")))
                .distinct()
                .collect(Collectors.joining(",")));

        mergedNode.setDeadlineName(approverNodes.stream()
                .map(WorkflowProcessNodeVo::getDeadlineName)
                .filter(Objects::nonNull)
                .flatMap(names -> Arrays.stream(names.split(",")))
                .distinct()
                .collect(Collectors.joining(",")));

        // 3. 合并结果
        List<WorkflowProcessNodeVo> result = new ArrayList<>(otherNodes);
        result.add(mergedNode);
        return result;
    }

    /**
     * 创建任务流程
     *
     * @param flowTaskDTO 流程任务DTO
     * @return 当前流程节点信息
     * <AUTHOR>
     * @since 2025-04-01
     */
    @Override
    @Transactional
    public CreateRespDTO createTask(FlowTaskDTO flowTaskDTO) {
        WorkflowProcessEntity workflowProcessEntity;

        if (flowTaskDTO.getKey() != null && !flowTaskDTO.getKey().isEmpty()) {
            workflowProcessEntity = getProcessEntity(flowTaskDTO.getKey());
            if (workflowProcessEntity != null) {
                flowTaskDTO.setProcessId(workflowProcessEntity.getId());
            }
        } else {
            workflowProcessEntity = getProcessEntity(flowTaskDTO.getProcessId());
        }

        if (workflowProcessEntity == null) {
            log.error("流程不存在：flowTaskDTO => {}",flowTaskDTO);
            throw new BusinessException("流程不存在");
        }

        LambdaQueryWrapper<WorkflowProcessInstEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkflowProcessInstEntity::getProcessId, flowTaskDTO.getProcessId());
        queryWrapper.eq(WorkflowProcessInstEntity::getBusinessId, flowTaskDTO.getBusinessId());
        queryWrapper.eq(WorkflowProcessInstEntity::getExtId, flowTaskDTO.getExtId());
        queryWrapper.notIn(WorkflowProcessInstEntity::getStatusCode, StatusEnum.REFUSE.getCode(), StatusEnum.REVOKE.getCode());

        int count = workflowProcessInstMapper.selectCount(queryWrapper);

        if (!flowTaskDTO.getIsSkip()) {
            if (count > 0) {
                log.error("工作流程已创建：flowTaskDTO => {}",flowTaskDTO);
                throw new BusinessException("工作流程已创建, 请勿重复创建");
            }
        }

        WorkflowProcessInstEntity workflowProcessInstEntity = new WorkflowProcessInstEntity();
        workflowProcessInstEntity.setProcessId(flowTaskDTO.getProcessId());
        workflowProcessInstEntity.setExtId(flowTaskDTO.getExtId());
        workflowProcessInstEntity.setBusinessId(flowTaskDTO.getBusinessId());
        workflowProcessInstEntity.setCreatedBy(SecurityUtil.getUserId());
        workflowProcessInstEntity.setStatusCode(StatusEnum.DEFAULT.getCode());
        workflowProcessInstEntity.setStatusText("");
        workflowProcessInstEntity.setParams(flowTaskDTO.getParams());
        workflowProcessInstMapper.insert(workflowProcessInstEntity);

        List<WorkflowProcessNodeEntity> list = getProcessNodes(flowTaskDTO.getProcessId(), flowTaskDTO.getExtId());
        if (CollectionUtils.isEmpty(list)) {
            log.error("流程节点不存在：flowTaskDTO => {}",flowTaskDTO);
            throw new BusinessException("流程节点不存在，请联系管理员进行配置");
        }

        WorkflowProcessNodeEntity updateNode = null;
        WorkflowProcedureEntity workflowProcedureEntity = null;
        for (WorkflowProcessNodeEntity node : list) {
            // 跳过类型直接跳过
            if (node.getType() == 0) {
                createProcedure(workflowProcessInstEntity, node, true);
            } else {
                workflowProcedureEntity = createProcedure(workflowProcessInstEntity, node);
                updateNode = node;
                // 找到第一个节点直接跳出.
                break;
            }
        }

        WorkflowProcessNodeDTO workflowProcessNodeDTO = new WorkflowProcessNodeDTO();
        workflowProcessNodeDTO.setWorkInstId(workflowProcessInstEntity.getId());
        if (updateNode != null) {
            BeanUtils.copyProperties(updateNode, workflowProcessNodeDTO);
            DeadlineCalc deadlineCalc = deadlineCalcFactory.create(workflowProcessNodeDTO);
            List<UserDTO> userDTOS = deadlineCalc.calc(workflowProcessInstEntity, workflowProcessNodeDTO, workflowProcedureEntity);
            if (CollectionUtils.isEmpty(userDTOS)) {
                log.error("未找到对应的节点审批人：flowTaskDTO => {}",flowTaskDTO);
                throw new BusinessException("未找到对应的节点审批人，请确认节点审批人配置是否正确");
            }
            workflowProcessNodeDTO.setUserList(userDTOS);
            workflowProcessInstEntity.setCurrentProcedureId(workflowProcedureEntity.getId());
            workflowProcessInstEntity.setDeadlineBy(workflowProcedureEntity.getCurrentProcessUsers());
            workflowProcessInstEntity.setStatusText(updateNode.getStatusText());
            workflowProcessInstEntity.setStatusCode(updateNode.getStatusCode());
            workflowProcessInstMapper.updateById(workflowProcessInstEntity);
        } else {
            log.warn("未找到有效流程节点或流程实体为空");
        }
        WorkflowProcessInstVo workflowProcessInstVo = new WorkflowProcessInstVo();
        BeanUtils.copyProperties(workflowProcessInstEntity, workflowProcessInstVo);

        CreateRespDTO createRespDTO = new CreateRespDTO();
        createRespDTO.setProcessNode(workflowProcessNodeDTO);
        createRespDTO.setProcessInst(workflowProcessInstVo);

        return createRespDTO;
    }

    /**
     * 审批任务
     *
     * @param approveTaskDTO 审批任务DTO
     * @return 结果信息
     * <AUTHOR>
     * @since 2025-04-01
     */
    @Override
    public ApprovalRespDTO approveTask(ApproveTaskDTO approveTaskDTO) {
        Long createdId = SecurityUtil.getUserId();

        WorkflowProcessInstEntity workflowProcessInstEntity = checkProcessInst(approveTaskDTO.getProcessId(),
                approveTaskDTO.getBusinessId(),
                approveTaskDTO.getInstanceId(),
                approveTaskDTO.getKey());

        LambdaQueryWrapper<WorkflowProcedureEntity> queryProcedureWrapper = new LambdaQueryWrapper<>();
        queryProcedureWrapper.eq(WorkflowProcedureEntity::getProcessInstId, workflowProcessInstEntity.getId());

        List<WorkflowProcedureEntity> workflowProcedureEntityList = workflowProcedureMapper.selectList(queryProcedureWrapper);
        List<WorkflowProcessNodeEntity> workflowProcessNodeEntityList = getProcessNodes(workflowProcessInstEntity.getProcessId(), workflowProcessInstEntity.getExtId());

        WorkflowProcessNodeEntity workflowProcessNodeEntity = findFirstMissingNode(workflowProcedureEntityList, workflowProcessNodeEntityList);

        WorkflowProcedureEntity currentWorkflowProcedureEntity = workflowProcedureMapper.selectById(workflowProcessInstEntity.getCurrentProcedureId());
        boolean isApproveUser = checkProcessUser(currentWorkflowProcedureEntity.getCurrentProcessUsers(), createdId);
        if (!approveTaskDTO.getIsSkip()) {
            if (!isApproveUser) {
                log.error("当前用户无审批权限,入参：approveTaskDTO => {},审批实例：workflowProcessInst => {}",approveTaskDTO,workflowProcessInstEntity);
                throw new BusinessException("当前用户无审批权限");
            }
        }

        log.info("currentWorkflowProcedureEntity {}", currentWorkflowProcedureEntity.getCurrentProcessUsers());

        currentWorkflowProcedureEntity.setDeadlineSuggestion(approveTaskDTO.getDeadlineSuggestion());
        if (!StringUtils.isEmpty(approveTaskDTO.getExtContent())) {
            currentWorkflowProcedureEntity.setExtContent(approveTaskDTO.getExtContent());
        }
        currentWorkflowProcedureEntity.setDeadlineResult(TaskResultEnum.AGREE.toString());
        currentWorkflowProcedureEntity.setDeadlineBy(createdId);
        currentWorkflowProcedureEntity.setEndDate(LocalDateTime.now());
        workflowProcedureMapper.updateById(currentWorkflowProcedureEntity);

        WorkflowProcessNodeEntity workflowProcessNode = workflowProcessNodeMapper.selectById(currentWorkflowProcedureEntity.getProcessNodeId());


        if (workflowProcessNodeEntity != null) {
            WorkflowProcedureEntity workflowProcedureEntity = createProcedure(workflowProcessInstEntity, workflowProcessNodeEntity);

            workflowProcessInstEntity.setStatusCode(workflowProcessNodeEntity.getStatusCode());
            workflowProcessInstEntity.setStatusText(workflowProcessNodeEntity.getStatusText());
            workflowProcessInstEntity.setCurrentProcedureId(workflowProcedureEntity.getId());
            workflowProcessInstEntity.setDeadlineBy(workflowProcedureEntity.getCurrentProcessUsers());

            workflowProcessInstMapper.updateById(workflowProcessInstEntity);
        } else {
            log.info("流程已完成");
            WorkflowProcessEntity processEntity = workflowProcessMapper.selectById(workflowProcessInstEntity.getProcessId());

            //完成待办
            if (processEntity.getIsTodo() == 1) {
                messageInfoService.doTodo(workflowProcessInstEntity.getBusinessId().toString(), processEntity.getKey());
            }

            workflowProcessInstEntity.setStatusCode(StatusEnum.FINISHED.getCode());
            workflowProcessInstEntity.setStatusText(StatusEnum.FINISHED.toString());
            workflowProcessInstMapper.updateById(workflowProcessInstEntity);

            // 查询是否有抄送人节点配置
            List<WorkflowProcessNodeEntity> ccNodeEntityList = workflowProcessNodeMapper.selectList(
                    new LambdaQueryWrapper<WorkflowProcessNodeEntity>()
                            .eq(WorkflowProcessNodeEntity::getProcessId, workflowProcessInstEntity.getProcessId())
                            .eq(WorkflowProcessNodeEntity::getExtId, workflowProcessInstEntity.getExtId())
                            .eq(WorkflowProcessNodeEntity::getIsApproverConfig, 1));
            // 给抄送人发送待办消息
            if (!CollectionUtils.isEmpty(ccNodeEntityList)) {
                this.sendToDoMessage(workflowProcessInstEntity);
            }
            this.sendCompletedMessage(TaskResultEnum.AGREE, workflowProcessInstEntity, currentWorkflowProcedureEntity);
        }
        if (workflowProcessNode.getHookName() != null && !workflowProcessNode.getHookName().isEmpty()) {
            log.info("触发发送消息：{}", workflowProcessNode.getHookName());
            HookDataDTO hookDataDTO = new HookDataDTO();
            WorkflowProcessInstVo workflowProcessInstVo = new WorkflowProcessInstVo();
            BeanUtils.copyProperties(workflowProcessInstEntity, workflowProcessInstVo);
            WorkflowProcedureVo workflowProcedureVo = new WorkflowProcedureVo();
            BeanUtils.copyProperties(currentWorkflowProcedureEntity, workflowProcedureVo);
            hookDataDTO.setWorkflowProcessInst(workflowProcessInstVo);
            hookDataDTO.setWorkflowProcedure(workflowProcedureVo);
            hookProducer.sendMessage(hookDataDTO, workflowProcessNode.getHookName());
        }

        return convertResponse(workflowProcessInstEntity, currentWorkflowProcedureEntity);
    }
    /**
     * 给抄送人发送待办消息，消息模板：{申请人} 发起的{**} 审批，已审批通过。
     * @Author: 武琦超
     * @Date: 2025/7/17 8:50
     * @param workflowProcessInstEntity 流程实例
     * @return: null
     **/

    private void sendToDoMessage(WorkflowProcessInstEntity workflowProcessInstEntity) {
        if (workflowProcessInstEntity != null) {
            WorkflowProcessEntity workflowProcessEntity = workflowProcessMapper.selectById(workflowProcessInstEntity.getProcessId());
            // 查询抄送人用户信息
            List<WorkflowProcessNodeEntity> workflowProcessNodeEntityList = getProcessNodesApprove(workflowProcessInstEntity.getProcessId(), workflowProcessInstEntity.getExtId(),1);
            List<WorkflowProcessNodeVo> workflowProcessNodeVoList = workflowProcessNodeEntityList.stream()
                    .map(entity -> {
                        WorkflowProcessNodeDTO workflowProcessNodeDTO = new WorkflowProcessNodeDTO();
                        WorkflowProcessNodeVo vo = new WorkflowProcessNodeVo();
                        BeanUtils.copyProperties(entity, vo);
                        BeanUtils.copyProperties(entity, workflowProcessNodeDTO);
                        if (entity.getType() != 0) {
                            DeadlineCalc deadlineCalc = deadlineCalcFactory.create(workflowProcessNodeDTO);
                            List<UserDTO> userDTOS = deadlineCalc.calc(workflowProcessInstEntity, workflowProcessNodeDTO, null);
                            if (CollectionUtils.isEmpty(userDTOS)) {
                                log.info("待办消息推送失败,未找到抄送人信息,节点对象 => {}", entity);
                            }
                            String ids = userDTOS.stream().map(user -> String.valueOf(user.getUserId())).collect(Collectors.joining(","));
                            vo.setDeadlineBy(userDTOS.isEmpty() ? null : ids);
                            vo.setUserList(userDTOS);
                        }
                        return vo;
                    })
                    .collect(Collectors.toList());
            // 处理抄送人的数据
            if (!CollectionUtils.isEmpty(workflowProcessNodeVoList)) {
                String userIds = workflowProcessNodeVoList.stream()
                        .map(WorkflowProcessNodeVo::getDeadlineBy)
                        .filter(Objects::nonNull)
                        .flatMap(ids -> Arrays.stream(ids.split(",")))
                        .distinct()
                        .collect(Collectors.joining(","));
                if (!StringUtils.isEmpty(userIds)) {
                    messageInfoService.sendToDoMessage(userIds, workflowProcessEntity, workflowProcessInstEntity);
                }
            }
        }
    }

    /**
     * 转换响应对象
     *
     * @param workflowProcessInstEntity      流程实例
     * @param currentWorkflowProcedureEntity 流程节点
     * @return 响应对象
     */
    private ApprovalRespDTO convertResponse(WorkflowProcessInstEntity workflowProcessInstEntity, WorkflowProcedureEntity currentWorkflowProcedureEntity) {
        ApprovalRespDTO respDTO = new ApprovalRespDTO();
        WorkflowProcedureVo workflowProcedureVo = new WorkflowProcedureVo();
        BeanUtils.copyProperties(currentWorkflowProcedureEntity, workflowProcedureVo);
        WorkflowProcessInstVo workflowProcessInstVo = new WorkflowProcessInstVo();
        BeanUtils.copyProperties(workflowProcessInstEntity, workflowProcessInstVo);

        respDTO.setWorkflowProcedure(workflowProcedureVo);
        respDTO.setProcessInstance(workflowProcessInstVo);

        return respDTO;
    }

    /**
     * 拒绝任务
     *
     * @param refuseTaskDTO 拒绝任务DTO
     */
    @Override
    public ApprovalRespDTO refuseTask(RefuseTaskDTO refuseTaskDTO) {
        Long createdId = SecurityUtil.getUserId();

        WorkflowProcessInstEntity workflowProcessInstEntity = checkProcessInst(refuseTaskDTO.getProcessId(),
                refuseTaskDTO.getBusinessId(),
                refuseTaskDTO.getInstanceId(),
                refuseTaskDTO.getKey());

        WorkflowProcedureEntity currentWorkflowProcedureEntity = workflowProcedureMapper.selectById(workflowProcessInstEntity.getCurrentProcedureId());
        boolean isApproveUser = checkProcessUser(currentWorkflowProcedureEntity.getCurrentProcessUsers(), createdId);
        if (!refuseTaskDTO.getIsSkip()) {
            if (!isApproveUser) {
                throw new BusinessException("当前用户无审批权限");
            }
        }

        workflowProcessInstEntity.setStatusCode(StatusEnum.REFUSE.getCode());
        workflowProcessInstEntity.setStatusText(StatusEnum.REFUSE.toString());
        workflowProcessInstMapper.updateById(workflowProcessInstEntity);

        currentWorkflowProcedureEntity.setDeadlineSuggestion(refuseTaskDTO.getDeadlineSuggestion());
        if (!StringUtils.isEmpty(refuseTaskDTO.getExtContent())) {
            currentWorkflowProcedureEntity.setExtContent(refuseTaskDTO.getExtContent());
        }
        currentWorkflowProcedureEntity.setDeadlineResult(TaskResultEnum.REFUSE.toString());
        currentWorkflowProcedureEntity.setDeadlineBy(createdId);
        currentWorkflowProcedureEntity.setEndDate(LocalDateTime.now());
        workflowProcedureMapper.updateById(currentWorkflowProcedureEntity);

        //完成代办事项
        WorkflowProcessEntity processEntity = workflowProcessMapper.selectById(workflowProcessInstEntity.getProcessId());
        if (processEntity.getIsTodo() == 1) {
            messageInfoService.doTodo(workflowProcessInstEntity.getBusinessId().toString(), processEntity.getKey());
        }

        this.sendCompletedMessage(TaskResultEnum.REFUSE, workflowProcessInstEntity, currentWorkflowProcedureEntity);

        return convertResponse(workflowProcessInstEntity, currentWorkflowProcedureEntity);
    }

    /**
     * 撤回任务
     *
     * @param revokeTaskDTO 撤回任务DTO
     */
    @Override
    public ApprovalRespDTO revokeTask(RevokeTaskDTO revokeTaskDTO) {
        WorkflowProcessInstEntity workflowProcessInstEntity = checkProcessInst(revokeTaskDTO.getProcessId(),
                revokeTaskDTO.getBusinessId(),
                revokeTaskDTO.getInstanceId(),
                revokeTaskDTO.getKey());

        Long createdId = SecurityUtil.getUserId();

        if (!revokeTaskDTO.getIsSkip() && !Objects.equals(workflowProcessInstEntity.getCreatedBy(), createdId)) {
            throw new BusinessException("当前用户不是流程发起人，无法撤回");
        }

        workflowProcessInstEntity.setStatusCode(StatusEnum.REVOKE.getCode());
        workflowProcessInstEntity.setStatusText(StatusEnum.REVOKE.toString());
        workflowProcessInstMapper.updateById(workflowProcessInstEntity);

        WorkflowProcedureEntity currentWorkflowProcedureEntity = workflowProcedureMapper.selectById(workflowProcessInstEntity.getCurrentProcedureId());
        currentWorkflowProcedureEntity.setDeadlineSuggestion(revokeTaskDTO.getDeadlineSuggestion());
        if (!StringUtils.isEmpty(revokeTaskDTO.getExtContent())) {
            currentWorkflowProcedureEntity.setExtContent(revokeTaskDTO.getExtContent());
        }
        currentWorkflowProcedureEntity.setDeadlineResult(TaskResultEnum.REVOKE.toString());
        currentWorkflowProcedureEntity.setDeadlineBy(createdId);
        currentWorkflowProcedureEntity.setEndDate(LocalDateTime.now());
        workflowProcedureMapper.updateById(currentWorkflowProcedureEntity);

        //完成代办事项
        WorkflowProcessEntity processEntity = workflowProcessMapper.selectById(workflowProcessInstEntity.getProcessId());
        if (processEntity.getIsTodo() == 1) {
            messageInfoService.doTodo(workflowProcessInstEntity.getBusinessId().toString(), processEntity.getKey());
        }

        this.sendCompletedMessage(TaskResultEnum.REVOKE, workflowProcessInstEntity, currentWorkflowProcedureEntity);

        return convertResponse(workflowProcessInstEntity, currentWorkflowProcedureEntity);
    }

    @Override
    public ApprovalRespDTO approvalTask(ApproveReqDTO flowTaskDTO) {
        ApprovalRespDTO respDTO;
        switch (flowTaskDTO.getApproveType()) {
            case AGREE:
                ApproveTaskDTO approveTaskDTO = new ApproveTaskDTO();
                BeanUtils.copyProperties(flowTaskDTO, approveTaskDTO);
                respDTO = approveTask(approveTaskDTO);
                break;
            case REFUSE:
                RefuseTaskDTO refuseTaskDTO = new RefuseTaskDTO();
                BeanUtils.copyProperties(flowTaskDTO, refuseTaskDTO);
                respDTO = refuseTask(refuseTaskDTO);
                break;
            case REVOKE:
                RevokeTaskDTO revokeTaskDTO = new RevokeTaskDTO();
                BeanUtils.copyProperties(flowTaskDTO, revokeTaskDTO);
                respDTO = revokeTask(revokeTaskDTO);
                break;
            default:
                log.error("审批类型错误,入参：flowTaskDTO => {}",flowTaskDTO);
                throw new BusinessException("审批类型错误");
        }

        return respDTO;
    }

    @Override
    public IPage<WorkflowProcessVO> getWorkFlowProcessList(Page<WorkflowProcessVO> page, GetWorkflowProcessFM getWorkflowProcessFM) {
        return workflowProcessMapper.getWorkFlowProcessList(page, getWorkflowProcessFM);
    }

    @Override
    @Transactional
    public String saveOrUpdateWorkflowProcess(WorkflowProcessFM workflowProcessFM) {
        String result = "";
        if (StringUtils.isEmpty(workflowProcessFM.getKey())) {
            List<WorkflowProcessEntity> workflowProcessEntityList = this.list(new LambdaQueryWrapper<WorkflowProcessEntity>().eq(WorkflowProcessEntity::getKey, workflowProcessFM.getKey()));
            if (!CollectionUtils.isEmpty(workflowProcessEntityList)) {
                return "流程key已存在，不可重复";
            }
        }
        WorkflowProcessEntity workflowProcessEntity = new WorkflowProcessEntity();
        BeanUtils.copyProperties(workflowProcessFM, workflowProcessEntity);
        workflowProcessEntity.setVersion(1);
        // 如果工作流有hook,和路由
        if (!StringUtils.isEmpty(workflowProcessEntity.getHookName())) {
            String queueName = workflowProcessEntity.getHookName() + ".queue";
            String routingKey = workflowProcessEntity.getHookName() + ".routing";

            log.info("RabbitMQ exchange bind routing [{}] to queue [{}]", routingKey, queueName);
            rabbitMQConfig.dynamicBind(queueName, routingKey);
        }
        this.saveOrUpdate(workflowProcessEntity);
        return result;
    }

    @Override
    public String removeWorkflowProcess(Long id) {
        String result = "";
        WorkflowProcessEntity workflowProcess = this.getById(id);
        if (workflowProcess != null) {
            List<WorkflowExtinfo> workflowExtinfoList = workflowExtinfoMapper.selectList(new LambdaQueryWrapper<WorkflowExtinfo>().eq(WorkflowExtinfo::getProcessId, id));
            if (!CollectionUtils.isEmpty(workflowExtinfoList)) {
                return "该工作流下有关联的扩展流程，请先删除对应的扩展流程";
            }
            Integer count = workflowProcessInstMapper.selectCount(new LambdaQueryWrapper<WorkflowProcessInstEntity>().eq(WorkflowProcessInstEntity::getProcessId, id));
            if (count > 0) {
                return "该工作流正在使用，无法删除";
            }
        }
        this.removeById(id);
        return result;
    }

    @Override
    public List<WorkflowProcessEntity> getWorkflowList() {
        return this.list();
    }

    @Override
    public List<WorkflowTreeListVO> getTreeList() {
        return this.baseMapper.getTreeList();
    }

    /**
     * 转移任务
     *
     * @param transferTaskDTO 转移任务DTO
     * @return 是否成功
     */
    @Override
    public boolean transferTask(TransferTaskDTO transferTaskDTO) {
        Long createdId = SecurityUtil.getUserId();

        WorkflowProcessInstEntity workflowProcessInstEntity = checkProcessInst(transferTaskDTO.getProcessId(),
                transferTaskDTO.getBusinessId(),
                transferTaskDTO.getInstanceId(),
                transferTaskDTO.getKey());
        WorkflowProcedureEntity currentWorkflowProcedureEntity = workflowProcedureMapper.selectById(
                workflowProcessInstEntity.getCurrentProcedureId());
        boolean isApproveUser = checkProcessUser(currentWorkflowProcedureEntity.getCurrentProcessUsers(), createdId);
        if (!isApproveUser) {
            throw new BusinessException("当前用户无审批权限");
        }

        //插入新的节点复制之前的数据
        WorkflowProcedureEntity workflowProcedureEntity = new WorkflowProcedureEntity();
        BeanUtils.copyProperties(currentWorkflowProcedureEntity, workflowProcedureEntity);
        // 新的处理人
        workflowProcedureEntity.setCurrentProcessUsers(transferTaskDTO.getToUserId().toString());
        workflowProcedureEntity.setId(null);
        workflowProcedureEntity.setStartDate(LocalDateTime.now());
        workflowProcedureEntity.setDeadlineDate(LocalDateTime.now());
        workflowProcedureMapper.insert(workflowProcedureEntity);

        //修改流程实例
        workflowProcessInstEntity.setDeadlineBy(transferTaskDTO.getToUserId().toString());
        workflowProcessInstEntity.setCurrentProcedureId(workflowProcedureEntity.getId());
        workflowProcessInstMapper.updateById(workflowProcessInstEntity);

        //修改旧数据
        currentWorkflowProcedureEntity.setDeadlineBy(createdId);
        currentWorkflowProcedureEntity.setDeadlineSuggestion(transferTaskDTO.getDeadlineSuggestion());
        currentWorkflowProcedureEntity.setDeadlineDate(LocalDateTime.now());
        currentWorkflowProcedureEntity.setEndDate(LocalDateTime.now());
        currentWorkflowProcedureEntity.setDeadlineResult(TaskResultEnum.TRANSFER.toString());
        workflowProcedureMapper.updateById(currentWorkflowProcedureEntity);

        return true;
    }

    @Override
    public WorkflowProcessEntity getWorkflowName(Long processId) {
        return this.getById(processId);
    }

    @Override
    public Set<String> getHooks() {
        List<WorkflowProcessEntity> list = this.workflowProcessMapper.selectList(null);
        Set<String> finalSets = list.stream().map(node -> {
            if (node.getHookName() != null && !node.getHookName().isEmpty()) {
                return node.getHookName();
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toSet());

        this.workflowProcessNodeMapper.selectList(null).forEach(node -> {
            if (node.getHookName() != null && !node.getHookName().isEmpty()) {
                finalSets.add(node.getHookName());
            }
        });

        return finalSets;
    }

    @Override
    public List<WorkflowProcessNodeVo> getNodeList(WorkflowProcessInstEntity workflowProcessInstEntity) {
        List<WorkflowProcessNodeVo> workflowProcessNodeVoList;
        if (workflowProcessInstEntity != null) {
            List<WorkflowProcessNodeEntity> workflowProcessNodeEntityList = getProcessNodesApprove(workflowProcessInstEntity.getProcessId(), workflowProcessInstEntity.getExtId(),0);
            workflowProcessNodeVoList = workflowProcessNodeEntityList.stream()
                    .map(entity -> {
                        WorkflowProcessNodeDTO workflowProcessNodeDTO = new WorkflowProcessNodeDTO();
                        WorkflowProcessNodeVo vo = new WorkflowProcessNodeVo();
                        BeanUtils.copyProperties(entity, vo);
                        BeanUtils.copyProperties(entity, workflowProcessNodeDTO);
                        if (entity.getType() != 0) {
                            DeadlineCalc deadlineCalc = deadlineCalcFactory.create(workflowProcessNodeDTO);
                            List<UserDTO> userDTOS = deadlineCalc.calc(workflowProcessInstEntity, workflowProcessNodeDTO, null);
                            if (CollectionUtils.isEmpty(userDTOS)) {
                                throw new BusinessException("未找到对应的节点审批人，请确认节点审批人配置是否正确");
                            }
                            String ids = userDTOS.stream().map(user -> String.valueOf(user.getUserId())).collect(Collectors.joining(","));
                            String names = userDTOS.stream().map(UserDTO::getRealName).collect(Collectors.joining(","));
                            vo.setDeadlineBy(userDTOS.isEmpty() ? null : ids);
                            vo.setDeadlineName(userDTOS.isEmpty() ? null : names);
                            vo.setUserList(userDTOS);
                        }
                        return vo;
                    })
                    .collect(Collectors.toList());
        } else {
            workflowProcessNodeVoList = new ArrayList<>();
        }
        return workflowProcessNodeVoList;
    }

    public List<WorkflowProcessNodeEntity> getProcessNodesApprove(Long id, Long extId, Integer isApproveUser) {
        LambdaQueryWrapper<WorkflowProcessNodeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkflowProcessNodeEntity::getProcessId, id);
        queryWrapper.eq(WorkflowProcessNodeEntity::getExtId, extId);
        queryWrapper.eq(WorkflowProcessNodeEntity::getIsApproverConfig, isApproveUser);
        queryWrapper.orderByAsc(WorkflowProcessNodeEntity::getIsApproverConfig);

        return workflowProcessNodeMapper.selectList(queryWrapper);
    }

    @Override
    public Boolean doTodo(Long instId) {
        if (instId != null) {
            // 查询流程实例
            WorkflowProcessInstEntity workflowProcessInstEntity = workflowProcessInstMapper.selectById(instId);
            if (workflowProcessInstEntity != null) {
                // 根据工作流id查询工作流对象
                WorkflowProcessEntity workflowProcessEntity = workflowProcessMapper.selectById(workflowProcessInstEntity.getProcessId());
                if (workflowProcessEntity != null) {
                    if (workflowProcessInstEntity.getStatusCode() == 99) {
                        return messageInfoService.doToDoMessage(workflowProcessInstEntity, workflowProcessEntity);
                    }
                }
            }
        }
        return true;
    }

    public List<InfraFileDTO> getFormItemFiles(List<Long> fileIds) {
        return this.workflowProcessMapper.getFormItemFiles(fileIds);
    }
}