package com.dh.workflow.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.common.util.R;
import com.dh.workflow.cmd.DeleteFlowableProcessInstanceCmd;
import com.dh.workflow.constant.CommentTypeEnum;
import com.dh.workflow.constant.FlowableConstant;
import com.dh.workflow.dao.FlowableProcessInstanceMapper;
import com.dh.workflow.dto.FlowableProcessInstanceDTO;
import com.dh.workflow.dto.FlowableProcessInstanceQueryDTO;
import com.dh.workflow.dto.StartProcessInstanceDTO;
import com.dh.workflow.dto.TaskDTO;
import com.dh.workflow.service.*;
import com.dh.workflow.util.SecurityUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.EndEvent;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.common.engine.impl.util.IoUtil;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.idm.api.User;
import org.flowable.task.api.Task;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2020/7/7
 */
@Slf4j
@Service
@AllArgsConstructor
public class FlowableProcessInstanceServiceImpl extends BaseProcessService implements IFlowableProcessInstanceService {

    private FlowableProcessInstanceMapper instanceMapper;

    private FlowProcessDiagramGenerator flowProcessDiagramGenerator;

    private IFlowableBpmnModelService flowableBpmnModelService;

    private IFlowableTaskService flowableTaskService;

    @Override
    public R start(StartProcessInstanceDTO processInstanceDto) {
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(processInstanceDto.getProcessDefinitionKey().trim())
                .latestVersion().singleResult();
        if(processDefinition == null){
            return R.failed("流程定义不存在!");
        }
        if(processDefinition.isSuspended()){
            return R.failed("此流程已经挂起，请联系管理员!");
        }

        //设置流程实例的发起人是当前用户
        Authentication.setAuthenticatedUserId(processInstanceDto.getCreateUserId());
        ProcessInstance processInstance = runtimeService.createProcessInstanceBuilder()
                .processDefinitionKey(processInstanceDto.getProcessDefinitionKey().trim())
                .name(processInstanceDto.getFormName().trim())
                .businessKey(processInstanceDto.getBusinessKey().trim())
                .variables(processInstanceDto.getVariables())
                .start();
        Authentication.setAuthenticatedUserId(null);

        log.info("流程启动成功！,流程实例id{}",processInstance.getId());
        return R.ok(processInstance.getId());
    }

    @Override
    public Map<String, Object> findProcessInstanceVariable(String processInstanceId) throws IOException,ClassNotFoundException {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if(processInstance != null){
            return runtimeService.getVariables(processInstanceId);
        }
        List<Map<String,Object>> list= instanceMapper.findHisVariable(processInstanceId);
        Map<String,Object> resultMap = new HashMap<>();
        for(Map<String,Object> map : list){
            String name = map.get("name").toString();
            Object text = null;
            if("serializable".equals(map.get("type").toString())){
                byte[] bytes = (byte[]) map.get("bytes");
                ObjectInputStream ois = new ObjectInputStream(new ByteArrayInputStream(bytes));
                if(name.contains("table")){
                    text = (LinkedHashMap) ois.readObject();
                }else if(name.contains("List")){
                    text = (ArrayList)ois.readObject();
                }else{
                    text = ois.readObject();
                }
            }else{
                text = map.get("text");
            }
            resultMap.put(name,text);
        }
        return resultMap;
    }

    @Override
    public R startByForm(StartProcessInstanceDTO processInstanceDto) {
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(processInstanceDto.getProcessDefinitionKey().trim())
                .latestVersion().singleResult();
        if(processDefinition == null){
            return R.failed("流程定义不存在!");
        }
        if(processDefinition.isSuspended()){
            return R.failed("此流程已经挂起，请联系管理员!");
        }

        //设置流程实例的发起人是当前用户
        ProcessInstance processInstance =  formService.submitStartFormData(processDefinition.getId(),processInstanceDto.getFormVariables());
        return R.ok(processInstance.getProcessInstanceId());
    }

    @Override
    public IPage<FlowableProcessInstanceDTO> findProcessInstance(Page<FlowableProcessInstanceDTO> page, FlowableProcessInstanceQueryDTO queryDto) {
        IPage<FlowableProcessInstanceDTO> instancePage =  instanceMapper.findProcessInstance(page,queryDto);
        for(FlowableProcessInstanceDTO instanceDto : instancePage.getRecords()){
            setStateApprover(instanceDto);
        }
        return instancePage;
    }

    @Override
    public IPage<FlowableProcessInstanceDTO> findMyProcessInstance(Page<FlowableProcessInstanceDTO> page) {
        FlowableProcessInstanceQueryDTO queryDto = new FlowableProcessInstanceQueryDTO();
        queryDto.setUserCode(SecurityUtil.getUserId().toString());
        IPage<FlowableProcessInstanceDTO> myProcesses = instanceMapper.findProcessInstance(page,queryDto);
        for(FlowableProcessInstanceDTO instanceDto : myProcesses.getRecords()){
            setStateApprover(instanceDto);
        }
        return myProcesses;
    }

    @Override
    public byte[] createImage(String processInstanceId) {
        //1.获取当前的流程实例
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        String processDefinitionId = null;
        List<String> activeActivityIds = new ArrayList<>();
        List<String> highLightedFlows = new ArrayList<>();
        //2.获取所有的历史轨迹线对象
        List<HistoricActivityInstance> historicSquenceFlows = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(processInstanceId).activityType(BpmnXMLConstants.ELEMENT_SEQUENCE_FLOW).list();
        historicSquenceFlows.forEach(historicActivityInstance -> highLightedFlows.add(historicActivityInstance.getActivityId()));
        //3. 获取流程定义id和高亮的节点id
        if (processInstance != null) {
            //3.1. 正在运行的流程实例
            processDefinitionId = processInstance.getProcessDefinitionId();
            activeActivityIds = runtimeService.getActiveActivityIds(processInstanceId);
        } else {
            //3.2. 已经结束的流程实例
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
            processDefinitionId = historicProcessInstance.getProcessDefinitionId();
            //3.3. 获取结束节点列表
            List<HistoricActivityInstance> historicEnds = historyService.createHistoricActivityInstanceQuery()
                    .processInstanceId(processInstanceId).activityType(BpmnXMLConstants.ELEMENT_EVENT_END).list();
            List<String> finalActiveActivityIds = activeActivityIds;
            historicEnds.forEach(historicActivityInstance -> finalActiveActivityIds.add(historicActivityInstance.getActivityId()));
        }
        //4. 获取bpmnModel对象
        BpmnModel bpmnModel = flowableBpmnModelService.getBpmnModelByProcessDefId(processDefinitionId);
        //5. 生成图片流
        InputStream inputStream = flowProcessDiagramGenerator.generateDiagram(bpmnModel, activeActivityIds, highLightedFlows);
        //6. 转化成byte便于网络传输
        return IoUtil.readInputStream(inputStream, "image inputStream name");
    }

    @Override
    public R deleteProcessInstanceById(String processInstanceId) {
        long count = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).count();
        if (count > 0) {
            DeleteFlowableProcessInstanceCmd cmd = new DeleteFlowableProcessInstanceCmd(processInstanceId, "删除流程实例", true);
            managementService.executeCommand(cmd);
        } else {
            historyService.deleteHistoricProcessInstance(processInstanceId);
        }
        return R.ok("删除成功");
    }

    @Override
    public R stopProcessInstanceById(String processInstanceId,String taskId,String message) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if(processInstance != null){
            //1.添加审批记录
            Task task = flowableTaskService.findTaskById(taskId);
            if(task == null){
                return R.failed("任务不存在！");
            }
            this.addComment(taskId,SecurityUtil.getUserId().toString(),processInstanceId, CommentTypeEnum.ZF.toString(),message);
            List<EndEvent> endNodes = flowableBpmnModelService.findEndFlowElement(processInstance.getProcessDefinitionId());
            String endId = endNodes.get(0).getId();
            //2.执行作废操作
            List<Execution> executions = runtimeService.createExecutionQuery().parentId(processInstanceId).list();
            List<String> executionIds = new ArrayList<>();
            executions.forEach(execution -> executionIds.add(execution.getId()));
            this.moveExecutionsToSingleActivityId(executionIds, endId);
            return R.ok("流程作废成功");
        }
        return R.failed("流程实例id不存在！");
    }

    /**
     * 设置状态和审批人
     */
    private void setStateApprover(FlowableProcessInstanceDTO processInstanceDto){
        if(processInstanceDto.getEndTime() == null){
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceDto.getProcessInstanceId())
                    .singleResult();
            if(processInstance.isSuspended()){ //挂起状态
                processInstanceDto.setSuspensionState(FlowableConstant.SUSPENSION_STATE);
            }else{
                processInstanceDto.setSuspensionState(FlowableConstant.ACTIVATE_STATE);
            }
        }
        List<User> approves = flowableTaskService.getApprovers(processInstanceDto.getProcessInstanceId());
        String userNames = this.createApprovers(approves);
        processInstanceDto.setApprover(userNames);
    }

    private String createApprovers(List<User> approves){
        if (CollectionUtils.isNotEmpty(approves)) {
            StringBuffer approverstr = new StringBuffer();
            StringBuffer finalApproverstr = approverstr;
            approves.forEach(user -> {
                finalApproverstr.append(user.getDisplayName()).append(";");
            });
            if (approverstr.length() > 0) {
                approverstr = approverstr.deleteCharAt(approverstr.length() - 1);
            }
            return approverstr.toString();
        }
        return null;
    }

}
