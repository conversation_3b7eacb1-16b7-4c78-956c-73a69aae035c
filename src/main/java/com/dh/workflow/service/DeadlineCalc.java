package com.dh.workflow.service;

import com.dh.dto.bean.dto.workflow.UserDTO;
import com.dh.dto.bean.dto.workflow.WorkflowProcessNodeDTO;
import com.dh.workflow.entity.WorkflowProcedureEntity;
import com.dh.workflow.entity.WorkflowProcessInstEntity;

import java.util.List;

/**
 * 当前处理人计算
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
public interface DeadlineCalc {
    /**
     * 计算当前节点的处理人
     *
     * @param workflowProcessNodeDTO 工作流节点
     * @return 处理人列表
     */
    List<UserDTO> calc(WorkflowProcessInstEntity workflowProcessInstEntity, WorkflowProcessNodeDTO workflowProcessNodeDTO, WorkflowProcedureEntity workflowProcedureEntity);

    /**
     * 节点类型
     *
     * @return 节点类型
     */
    Integer getType();
}
