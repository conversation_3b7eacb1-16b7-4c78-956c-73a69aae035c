package com.dh.workflow.service;

import com.dh.dto.bean.dto.workflow.WorkflowProcessNodeDTO;
import com.dh.framework.security.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 当前节点处理人计算工厂
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Component
public class DeadlineCalcFactory {
    private final List<DeadlineCalc> handlers;
    private Map<Integer, DeadlineCalc> calcMap;

    @Autowired
    public DeadlineCalcFactory(List<DeadlineCalc> handlers) {
        this.handlers = handlers;
    }

    @PostConstruct
    public void init() {
        calcMap = handlers.stream().collect(Collectors.toMap(DeadlineCalc::getType, Function.identity()));
    }

    /**
     * 创建当前节点的处理人计算接口
     *
     * @param workflowProcessNodeDTO 工作流节点
     * @return DeadlineCalc
     */
    public DeadlineCalc create(WorkflowProcessNodeDTO workflowProcessNodeDTO) {
        DeadlineCalc deadlineCalc = calcMap.get(workflowProcessNodeDTO.getType());

        if (deadlineCalc == null) {
            throw new BusinessException("不支持的节点类型: " + workflowProcessNodeDTO.getType());
        }

        return deadlineCalc;
    }
}
