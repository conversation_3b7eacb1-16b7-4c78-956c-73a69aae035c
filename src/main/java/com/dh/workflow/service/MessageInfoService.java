package com.dh.workflow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dh.dto.bean.dto.workflow.UserDTO;
import com.dh.dto.bean.dto.workflow.vo.WorkflowProcessInstVo;
import com.dh.workflow.entity.MessageInfo;
import com.dh.workflow.entity.WorkflowProcessEntity;
import com.dh.workflow.entity.WorkflowProcessInstEntity;

import java.util.List;

/**
 * <p>
 * 消息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-11
 */

public interface MessageInfoService extends IService<MessageInfo> {
    /**
     * 添加代办事项
     * @param userList 用户列表
     * @param workflowProcessInst 流程实例
     * @param msgUrl 代办的url
     */
    void addTodo(List<UserDTO> userList, WorkflowProcessInstEntity workflowProcessInst, WorkflowProcessEntity processEntity, String msgUrl);

    /**
     * 设置代办事项为已办
     * @param businessId 业务ID
     * @param businessType 代办类型
     * @return true: 成功，false: 失败
     */
    boolean doTodo(String businessId, String businessType);

    /**
     * 给抄送人发送待办
     * @Author: 武琦超
     * @Date: 2025/7/17 9:31
     * @param userIds 用户id字符串
     * @param workflowProcessEntity 工作流对象
     * @param workflowProcessInstEntity 工作流实例对象
     * @return: null
     **/
    void sendToDoMessage(String userIds, WorkflowProcessEntity workflowProcessEntity, WorkflowProcessInstEntity workflowProcessInstEntity);

    /**
     * 如果审批通过，修改待办为已办
     * @Author: 武琦超
     * @Date: 2025/7/17 9:50
     * @param workflowProcessEntity 工作流对象
     * @param workflowProcessInstEntity 工作流实例对象
     * @return: null
     **/
    boolean doToDoMessage(WorkflowProcessInstEntity workflowProcessInstEntity, WorkflowProcessEntity workflowProcessEntity);
}
