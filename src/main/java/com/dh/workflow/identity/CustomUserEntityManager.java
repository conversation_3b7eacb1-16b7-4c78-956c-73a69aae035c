package com.dh.workflow.identity;

import com.dh.common.dto.SysUserDTO;
import com.dh.common.util.R;
import com.dh.workflow.config.DhPropertiesConfig;
import com.dh.workflow.dto.FlowableUserQueryDTO;
import com.dh.workflow.util.SecurityUtil;
import org.flowable.idm.api.User;
import org.flowable.idm.engine.IdmEngineConfiguration;
import org.flowable.idm.engine.impl.UserQueryImpl;
import org.flowable.idm.engine.impl.persistence.entity.UserEntity;
import org.flowable.idm.engine.impl.persistence.entity.UserEntityImpl;
import org.flowable.idm.engine.impl.persistence.entity.UserEntityManagerImpl;
import org.flowable.idm.engine.impl.persistence.entity.data.UserDataManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/10
 */
public class CustomUserEntityManager extends UserEntityManagerImpl {

    @Autowired
    private DhPropertiesConfig dhPropertiesConfig;

    @Autowired
    private RestTemplate restTemplate;

    public CustomUserEntityManager(IdmEngineConfiguration idmEngineConfiguration, UserDataManager userDataManager) {
        super(idmEngineConfiguration, userDataManager);
    }

    @Override
    public UserEntity findById(String userId) {
        ResponseEntity<R<SysUserDTO>> responseEntity = restTemplate.exchange(dhPropertiesConfig.getIdentityUserUrl()+"/"+userId+"?token=" + SecurityUtil.getToken()
                , HttpMethod.GET
                ,null
                , new ParameterizedTypeReference<R<SysUserDTO>>(){});
        R<SysUserDTO> result = responseEntity.getBody();
        if(result != null && "0".equals(result.getCode())) {
            SysUserDTO sysUser = result.getData();
            UserEntity user = new UserEntityImpl();
            user.setFirstName(sysUser.getUsername());
            user.setLastName(sysUser.getUsername());
            user.setDisplayName(sysUser.getRealName());
            user.setEmail(sysUser.getEmail());
            return user;
        }
        return null;
    }

    @Override
    public List<User> findUserByQueryCriteria(UserQueryImpl query) {
        return flowableQueryToWapper(query);
    }

    @Override
    public long findUserCountByQueryCriteria(UserQueryImpl query) {
        return flowableQueryToWapper(query).size();
    }

    private List<User> flowableQueryToWapper(UserQueryImpl query){
        FlowableUserQueryDTO userQueryDTO = new FlowableUserQueryDTO();
        userQueryDTO.setId(StringUtils.isEmpty(query.getId()) ? null: Long.parseLong(query.getId()));
        userQueryDTO.setUsername(query.getFirstName());
        userQueryDTO.setUsernameLike(query.getFirstNameLike());
        userQueryDTO.setGroupId(StringUtils.isEmpty(query.getGroupId()) ? null : Long.parseLong(query.getGroupId()));

        if(!CollectionUtils.isEmpty(query.getIds())){
            List<Long> ids = new ArrayList<>();
            query.getIds().forEach(i->ids.add(Long.parseLong(i)));
            userQueryDTO.setIds(ids);
        }

        if(!CollectionUtils.isEmpty(query.getGroupIds())){
            List<Long> groupIds = new ArrayList<>();
            query.getGroupIds().forEach(i->groupIds.add(Long.parseLong(i)));
            userQueryDTO.setGroupIds(groupIds);
        }

        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json;charset=UTF-8");
        headers.setContentType(type);

        HttpEntity<FlowableUserQueryDTO> objHttpEntity = new HttpEntity<>(userQueryDTO,headers);
        ResponseEntity<R<List<SysUserDTO>>> responseEntity = restTemplate.exchange(dhPropertiesConfig.getIdentityUserListUrl()+"?token=" + SecurityUtil.getToken()
                , HttpMethod.POST
                ,objHttpEntity
                , new ParameterizedTypeReference<R<List<SysUserDTO>>>(){});
        R<List<SysUserDTO>> result = responseEntity.getBody();
        List<User> userList = new ArrayList<>();
        if(result != null && "0".equals(result.getCode()) && result.getData().size() > 0) {
            List<SysUserDTO> sysUserList = result.getData();
            for(SysUserDTO sysUser :sysUserList){
                User u = new UserEntityImpl();
                u.setFirstName(sysUser.getUsername());
                u.setLastName(sysUser.getUsername());
                u.setDisplayName(sysUser.getRealName());
                u.setEmail(sysUser.getEmail());
                userList.add(u);
            }
            return userList;
        }
        return userList;
    }
}
