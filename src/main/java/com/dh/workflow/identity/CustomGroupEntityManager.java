package com.dh.workflow.identity;

import com.dh.common.util.R;
import com.dh.workflow.config.DhPropertiesConfig;
import com.dh.workflow.dto.FlowableGroupDTO;
import com.dh.workflow.dto.FlowableGroupQueryDTO;
import com.dh.workflow.util.SecurityUtil;
import org.flowable.idm.api.Group;
import org.flowable.idm.engine.IdmEngineConfiguration;
import org.flowable.idm.engine.impl.GroupQueryImpl;
import org.flowable.idm.engine.impl.persistence.entity.GroupEntity;
import org.flowable.idm.engine.impl.persistence.entity.GroupEntityImpl;
import org.flowable.idm.engine.impl.persistence.entity.GroupEntityManagerImpl;
import org.flowable.idm.engine.impl.persistence.entity.data.GroupDataManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/7/10
 */
public class CustomGroupEntityManager extends GroupEntityManagerImpl {

    @Autowired
    private DhPropertiesConfig dhPropertiesConfig;

    @Autowired
    private RestTemplate restTemplate;

    public CustomGroupEntityManager(IdmEngineConfiguration idmEngineConfiguration, GroupDataManager groupDataManager) {
        super(idmEngineConfiguration, groupDataManager);
    }

    @Override
    public GroupEntity findById(String groupId) {
        ResponseEntity<R<FlowableGroupDTO>> responseEntity = restTemplate.exchange(dhPropertiesConfig.getIdentityGroupUrl()+"/"+groupId+"?token=" + SecurityUtil.getToken()
                , HttpMethod.GET
                ,null
                , new ParameterizedTypeReference<R<FlowableGroupDTO>>(){});
        R<FlowableGroupDTO> result = responseEntity.getBody();
        if(result != null && "0".equals(result.getCode())) {
            FlowableGroupDTO groupDTO = result.getData();
            GroupEntity groupEntity = new GroupEntityImpl();
            groupEntity.setId(groupDTO.getId());
            groupEntity.setName(groupDTO.getName());
            groupEntity.setType(groupDTO.getType());
            return groupEntity;
        }
        return null;
    }

    @Override
    public long findGroupCountByQueryCriteria(GroupQueryImpl query) {
        return flowablQueryToWapper(query).size();
    }

    @Override
    public List<Group> findGroupByQueryCriteria(GroupQueryImpl query) {
        return flowablQueryToWapper(query);
    }

    private List<Group> flowablQueryToWapper(GroupQueryImpl query){
        FlowableGroupQueryDTO groupQueryDTO = new FlowableGroupQueryDTO();
        groupQueryDTO.setId((StringUtils.isEmpty(query.getId()) ? null : Long.parseLong(query.getId())));
        groupQueryDTO.setName(query.getName());
        groupQueryDTO.setNamelike(query.getNameLike());
        groupQueryDTO.setUserId(StringUtils.isEmpty(query.getUserId())?null : Long.parseLong(query.getUserId()));
        if(!CollectionUtils.isEmpty(query.getIds())){
            List<Long> ids = new ArrayList<>();
            query.getIds().forEach(i -> ids.add(Long.parseLong(i)));
            groupQueryDTO.setIds(ids);
        }
        if(!CollectionUtils.isEmpty(query.getUserIds())){
            List<Long> userIds = new ArrayList<>();
            query.getUserIds().forEach(i -> userIds.add(Long.parseLong(i)));
            groupQueryDTO.setUserIds(userIds);
        }

        HttpEntity<FlowableGroupQueryDTO> objHttpEntity = new HttpEntity<>(groupQueryDTO);
        ResponseEntity<R<List<FlowableGroupDTO>>> responseEntity = restTemplate.exchange(dhPropertiesConfig.getIdentityGroupListUrl()+"?token=" + SecurityUtil.getToken()
                , HttpMethod.POST
                ,objHttpEntity
                , new ParameterizedTypeReference<R<List<FlowableGroupDTO>>>(){});
        R<List<FlowableGroupDTO>> result = responseEntity.getBody();
        List<Group> groupList = new ArrayList<>();
        if(result != null && "0".equals(result.getCode()) && result.getData().size() > 0){
            List<FlowableGroupDTO> groupDTOList = result.getData();
            for(FlowableGroupDTO groupDTO : groupDTOList){
                Group group = new GroupEntityImpl();
                group.setId(groupDTO.getId());
                group.setName(groupDTO.getName());
                group.setType(groupDTO.getType());
                groupList.add(group);
            }
            return groupList;
        }
        return groupList;
    }
}
