package com.dh.workflow.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 实体对象字段自动填充处理器
 * 处理 createdAt 和 updatedAt 字段
 *
 * <AUTHOR>
 * @since 2025/05/13
 */
@Slf4j
@Component
@Primary
public class EntityMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        log.info("start insert fill");
        // 创建时间使用当前时间填充
        this.setFieldValByName("createdAt", LocalDateTime.now(), metaObject);
        // 更新时间使用当前时间填充
        this.setFieldValByName("updatedAt", LocalDateTime.now(), metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("start update fill");
        // 更新时间使用当前时间填充
        this.setFieldValByName("updatedAt", LocalDateTime.now(), metaObject);
    }
}
