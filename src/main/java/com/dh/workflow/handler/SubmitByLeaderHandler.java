package com.dh.workflow.handler;

import com.dh.workflow.dao.SysUserMapper;
import com.dh.workflow.entity.SysUser;
import com.dh.workflow.util.SpringContextUtil;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/8/19
 */
public class SubmitByLeaderHandler implements TaskListener {

    private static RuntimeService runtimeService = SpringContextUtil.getBean(RuntimeService.class);

    private static SysUserMapper sysUserMapper = SpringContextUtil.getBean(SysUserMapper.class);

    @Override
    public void notify(DelegateTask delegateTask) {
        String processInstanceId = delegateTask.getProcessInstanceId();
        Map<String,Object> map = runtimeService.getVariables(processInstanceId);
        String submitBy = map.get("submitBy") == null?"":map.get("submitBy").toString();
        if(!"".equals(submitBy)){
            SysUser sysUser = sysUserMapper.findDeptLeader(submitBy);
            if(sysUser != null){
                delegateTask.addCandidateUser(sysUser.getId().toString());
            }
        }
    }
}
